"""
Chatbot-specific configuration settings
"""

import os
from dotenv import load_dotenv

load_dotenv()

# Gemini AI Configuration
GEMINI_MODEL = "gemini-1.5-flash"
TEMPERATURE = float(os.getenv('TEMPERATURE', 0.3))  # Lower temperature for more focused responses
MAX_TOKENS = int(os.getenv('MAX_TOKENS', 1500))  # Increased for more detailed responses
TOP_P = 0.9  # Higher for more diverse vocabulary
TOP_K = 30   # Lower for more focused responses

# Safety Settings
SAFETY_SETTINGS = [
    {
        "category": "HARM_CATEGORY_HARASSMENT",
        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
    },
    {
        "category": "HARM_CATEGORY_HATE_SPEECH",
        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
    },
    {
        "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
    },
    {
        "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
    }
]

# Generation Configuration
GENERATION_CONFIG = {
    "temperature": TEMPERATURE,
    "top_p": TOP_P,
    "top_k": TOP_K,
    "max_output_tokens": MAX_TOKENS,
}

# Enhanced System Prompt for the Chatbot
SYSTEM_PROMPT = """
You are MMMUT Assistant, an expert admission counselor for MMMUT (Madan Mohan Malaviya University of Technology), Gorakhpur, Uttar Pradesh, India.

🎯 YOUR MISSION: Provide specific, actionable, and personalized guidance to prospective students about MMMUT admissions.

🔑 CORE PRINCIPLES:
1. **SPECIFICITY OVER GENERALITY**: Always provide exact numbers, dates, requirements, and procedures
2. **CONTEXT-AWARE RESPONSES**: Reference the specific information from the knowledge base provided
3. **ACTIONABLE GUIDANCE**: Tell users exactly what they need to do next
4. **PERSONALIZED ASSISTANCE**: Address their specific situation and concerns
5. **ENCOURAGING TONE**: Be supportive while being informative

📋 RESPONSE STRUCTURE (MANDATORY):
1. **Direct Answer**: Start with a clear, specific answer to their exact question
2. **Detailed Information**: Provide comprehensive details with numbers, dates, requirements
3. **Next Steps**: Tell them exactly what to do next
4. **Additional Context**: Share related information they might need
5. **Call to Action**: End with specific next steps or offer further assistance

✅ MUST INCLUDE WHEN RELEVANT:
• Exact fees (₹1,00,000 annual fee breakdown)
• Specific dates (Application: March 1 - April 30, 2024)
• Precise eligibility (75% in 10+2 with PCM, JEE Main qualification)
• Contact details (+91-551-2273958, <EMAIL>)
• Course names (CSE, IT, ECE, EE, ME, CE, Chemical, Biotechnology)
• Placement stats (85% placement, 6.5 LPA average, 25 LPA highest)

🎨 FORMATTING REQUIREMENTS:
• Use emojis for visual appeal and categorization
• Use **bold** for important information
• Use bullet points for lists
• Use numbered steps for processes
• Create clear sections with headers

🚫 AVOID:
• Generic responses like "contact the office for more details"
• Vague statements without specific numbers or dates
• Long paragraphs without structure
• Information not supported by the knowledge base

💡 EXAMPLE RESPONSE STYLE:
"🎓 **For B.Tech CSE Admission at MMMUT:**

**Eligibility Requirements:**
• 10+2 with Physics, Chemistry, Mathematics
• Minimum 75% marks in 10+2
• Valid JEE Main score

**Annual Fee Structure:**
• Tuition Fee: ₹50,000
• Hostel Fee: ₹25,000
• Mess Fee: ₹20,000
• Other Charges: ₹5,000
• **Total: ₹1,00,000 per year**

**Next Steps:**
1. Check your 10+2 marks (need 75%+)
2. Register for JEE Main if not done
3. Apply online from March 1-April 30, 2024

📞 **Need Help?** Call +91-551-2273958 <NAME_EMAIL>

What specific aspect would you like me to explain further?"

Remember: Every response should be so detailed and specific that the student knows exactly what to do next without needing to ask follow-up questions.
"""

# Response Templates
RESPONSE_TEMPLATES = {
    'course_info': "📚 **Course Information for {course_name} at MMMUT:**\n\n{details}\n\n💡 *Need more specific details? Feel free to ask!*",
    'eligibility': "✅ **Eligibility Criteria for {course_name}:**\n\n{criteria}\n\n📝 *Have questions about your eligibility? I'm here to help!*",
    'fees': "💰 **Fee Structure for {course_name}:**\n\n{fee_details}\n\n💳 *Questions about payment options or scholarships? Just ask!*",
    'dates': "📅 **Important Admission Dates:**\n\n{dates}\n\n⏰ *Don't miss these deadlines! Set reminders for important dates.*",
    'contact': "📞 **MMMUT Admission Office Contact:**\n\n{contact_details}\n\n🤝 *They're ready to help with your specific queries!*",
    'facilities': "🏫 **MMMUT Campus Facilities:**\n\n{facility_details}\n\n🌟 *Want to know more about campus life? Ask away!*",
    'placement': "🎯 **Placement Information:**\n\n{placement_details}\n\n🚀 *Interested in career prospects? I can share more details!*"
}

# Query Categories for Intent Recognition
QUERY_CATEGORIES = {
    'courses': ['course', 'program', 'branch', 'stream', 'degree', 'btech', 'engineering'],
    'eligibility': ['eligibility', 'criteria', 'qualification', 'marks', 'percentage', 'requirement'],
    'fees': ['fee', 'cost', 'payment', 'scholarship', 'financial', 'money'],
    'dates': ['date', 'deadline', 'schedule', 'timeline', 'when', 'last date'],
    'admission': ['admission', 'apply', 'application', 'form', 'procedure', 'process'],
    'facilities': ['facility', 'hostel', 'library', 'lab', 'infrastructure', 'campus'],
    'placement': ['placement', 'job', 'career', 'company', 'recruitment', 'salary'],
    'contact': ['contact', 'phone', 'email', 'address', 'office', 'help']
}

# Confidence Thresholds
MIN_CONFIDENCE_THRESHOLD = 0.6
HIGH_CONFIDENCE_THRESHOLD = 0.8

# Rate Limiting
MAX_REQUESTS_PER_MINUTE = 30
MAX_REQUESTS_PER_HOUR = 500