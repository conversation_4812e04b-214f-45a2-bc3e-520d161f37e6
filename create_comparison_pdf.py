#!/usr/bin/env python3
"""
Create PDF comparison document from Q&A data
"""

import sys
import json
import argparse
from datetime import datetime
from pathlib import Path

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
except ImportError:
    print("❌ ReportLab not installed. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY

def create_custom_styles():
    """Create custom paragraph styles"""
    styles = getSampleStyleSheet()
    
    # Title style
    styles.add(ParagraphStyle(
        name='CustomTitle',
        parent=styles['Title'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    ))
    
    # Heading style
    styles.add(ParagraphStyle(
        name='CustomHeading',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.darkgreen
    ))
    
    # Question style
    styles.add(ParagraphStyle(
        name='Question',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=8,
        spaceBefore=8,
        textColor=colors.darkblue,
        fontName='Helvetica-Bold'
    ))
    
    # Answer style
    styles.add(ParagraphStyle(
        name='Answer',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=12,
        alignment=TA_JUSTIFY,
        leftIndent=20
    ))
    
    # Expected answer style
    styles.add(ParagraphStyle(
        name='ExpectedAnswer',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=12,
        alignment=TA_JUSTIFY,
        leftIndent=20,
        textColor=colors.darkgreen
    ))
    
    # Metadata style
    styles.add(ParagraphStyle(
        name='Metadata',
        parent=styles['Normal'],
        fontSize=9,
        spaceAfter=6,
        textColor=colors.grey,
        leftIndent=20
    ))
    
    return styles

def create_summary_table(comparison_data):
    """Create summary statistics table"""
    # Calculate statistics
    total_questions = len(comparison_data)
    categories = {}
    response_types = {}
    confidence_levels = {'high': 0, 'medium': 0, 'low': 0}
    errors = 0
    
    for item in comparison_data:
        # Count categories
        category = item['category']
        categories[category] = categories.get(category, 0) + 1
        
        # Count response types
        resp_type = item['response_type']
        response_types[resp_type] = response_types.get(resp_type, 0) + 1
        
        # Count confidence levels
        confidence = item['confidence']
        if confidence >= 0.8:
            confidence_levels['high'] += 1
        elif confidence >= 0.5:
            confidence_levels['medium'] += 1
        else:
            confidence_levels['low'] += 1
        
        # Count errors
        if item['has_error']:
            errors += 1
    
    # Create summary data
    summary_data = [
        ['Metric', 'Value'],
        ['Total Questions Tested', str(total_questions)],
        ['Questions with Errors', str(errors)],
        ['Success Rate', f"{((total_questions - errors) / total_questions * 100):.1f}%"],
        ['', ''],
        ['Response Types', ''],
    ]
    
    for resp_type, count in response_types.items():
        summary_data.append([f"  {resp_type.replace('_', ' ').title()}", str(count)])
    
    summary_data.extend([
        ['', ''],
        ['Confidence Levels', ''],
        ['  High (≥80%)', str(confidence_levels['high'])],
        ['  Medium (50-79%)', str(confidence_levels['medium'])],
        ['  Low (<50%)', str(confidence_levels['low'])],
        ['', ''],
        ['Categories Tested', ''],
    ])
    
    for category, count in sorted(categories.items()):
        summary_data.append([f"  {category.replace('_', ' ').title()}", str(count)])
    
    return summary_data

def create_pdf_document(comparison_data, output_filename):
    """Create the PDF document"""
    doc = SimpleDocTemplate(
        output_filename,
        pagesize=A4,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=18
    )
    
    # Get styles
    styles = create_custom_styles()
    story = []
    
    # Title page
    story.append(Paragraph("MMMUT Admission Chatbot", styles['CustomTitle']))
    story.append(Paragraph("Question & Answer Comparison Report", styles['CustomTitle']))
    story.append(Spacer(1, 0.5*inch))
    
    # Metadata
    metadata = comparison_data.get('metadata', {})
    story.append(Paragraph(f"<b>Generation Date:</b> {metadata.get('generation_date', 'Unknown')}", styles['Normal']))
    story.append(Paragraph(f"<b>Chatbot Version:</b> {metadata.get('chatbot_version', 'Unknown')}", styles['Normal']))
    story.append(Paragraph(f"<b>Total Questions:</b> {metadata.get('total_questions', 0)}", styles['Normal']))
    story.append(Spacer(1, 0.3*inch))
    
    # Summary statistics
    story.append(Paragraph("Executive Summary", styles['CustomHeading']))
    summary_data = create_summary_table(comparison_data['comparison_data'])
    
    summary_table = Table(summary_data, colWidths=[3*inch, 1.5*inch])
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
    ]))
    
    story.append(summary_table)
    story.append(PageBreak())
    
    # Detailed Q&A comparison
    story.append(Paragraph("Detailed Question & Answer Comparison", styles['CustomHeading']))
    story.append(Spacer(1, 0.2*inch))
    
    qa_data = comparison_data['comparison_data']
    
    for i, item in enumerate(qa_data, 1):
        # Question header
        story.append(Paragraph(f"Question {i}: {item['category'].replace('_', ' ').title()}", styles['CustomHeading']))
        
        # Question text
        story.append(Paragraph(f"<b>Q:</b> {item['question']}", styles['Question']))
        
        # Expected answer
        story.append(Paragraph("<b>Expected Answer:</b>", styles['Normal']))
        story.append(Paragraph(item['expected_answer'], styles['ExpectedAnswer']))
        
        # Chatbot response
        story.append(Paragraph("<b>Chatbot Response:</b>", styles['Normal']))
        story.append(Paragraph(item['chatbot_response'], styles['Answer']))
        
        # Metadata
        metadata_text = f"Source: {item['source']} | Type: {item['response_type']} | Confidence: {item['confidence']:.2f}"
        if item['sources']:
            metadata_text += f" | Sources: {', '.join(item['sources'])}"
        if item['has_error']:
            metadata_text += " | ⚠️ Error occurred"
        
        story.append(Paragraph(metadata_text, styles['Metadata']))
        story.append(Spacer(1, 0.3*inch))
        
        # Page break every 3 questions to avoid overcrowding
        if i % 3 == 0 and i < len(qa_data):
            story.append(PageBreak())
    
    # Build PDF
    doc.build(story)
    return output_filename

def main():
    parser = argparse.ArgumentParser(description='Create PDF comparison document from Q&A JSON data')
    parser.add_argument('json_file', help='Input JSON file with comparison data')
    parser.add_argument('-o', '--output', help='Output PDF filename', default=None)
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not Path(args.json_file).exists():
        print(f"❌ Input file not found: {args.json_file}")
        sys.exit(1)
    
    # Load comparison data
    try:
        with open(args.json_file, 'r', encoding='utf-8') as f:
            comparison_data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading JSON file: {e}")
        sys.exit(1)
    
    # Generate output filename
    if args.output:
        output_filename = args.output
    else:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_filename = f"MMMUT_Chatbot_QA_Comparison_{timestamp}.pdf"
    
    # Create PDF
    try:
        print(f"📄 Creating PDF document...")
        create_pdf_document(comparison_data, output_filename)
        print(f"✅ PDF created successfully: {output_filename}")
        
        # Print summary
        total_questions = len(comparison_data['comparison_data'])
        errors = sum(1 for item in comparison_data['comparison_data'] if item['has_error'])
        success_rate = (total_questions - errors) / total_questions * 100
        
        print(f"\n📊 Summary:")
        print(f"   Total Questions: {total_questions}")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   PDF Size: {Path(output_filename).stat().st_size / 1024:.1f} KB")
        
    except Exception as e:
        print(f"❌ Error creating PDF: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
