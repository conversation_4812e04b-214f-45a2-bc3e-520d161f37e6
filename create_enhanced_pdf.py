#!/usr/bin/env python3
"""
Create enhanced PDF comparison document with detailed analysis
"""

import sys
import json
import argparse
from datetime import datetime
from pathlib import Path

from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
from reportlab.graphics.shapes import Drawing, Rect
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics import renderPDF

def create_enhanced_styles():
    """Create enhanced paragraph styles"""
    styles = getSampleStyleSheet()
    
    # Title style
    styles.add(ParagraphStyle(
        name='MainTitle',
        parent=styles['Title'],
        fontSize=28,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue,
        fontName='Helvetica-Bold'
    ))
    
    # Subtitle style
    styles.add(ParagraphStyle(
        name='Subtitle',
        parent=styles['Title'],
        fontSize=18,
        spaceAfter=20,
        alignment=TA_CENTER,
        textColor=colors.darkgreen
    ))
    
    # Section heading
    styles.add(ParagraphStyle(
        name='SectionHeading',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.darkblue,
        fontName='Helvetica-Bold'
    ))
    
    # Question style
    styles.add(ParagraphStyle(
        name='QuestionStyle',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=8,
        spaceBefore=8,
        textColor=colors.darkblue,
        fontName='Helvetica-Bold',
        leftIndent=10
    ))
    
    # Expected answer style
    styles.add(ParagraphStyle(
        name='ExpectedStyle',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=8,
        alignment=TA_JUSTIFY,
        leftIndent=20,
        textColor=colors.darkgreen,
        borderColor=colors.lightgreen,
        borderWidth=1,
        borderPadding=5
    ))
    
    # Chatbot answer style
    styles.add(ParagraphStyle(
        name='ChatbotStyle',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=12,
        alignment=TA_JUSTIFY,
        leftIndent=20,
        textColor=colors.black,
        borderColor=colors.lightblue,
        borderWidth=1,
        borderPadding=5
    ))
    
    # Analysis style
    styles.add(ParagraphStyle(
        name='AnalysisStyle',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=10,
        textColor=colors.grey,
        leftIndent=20,
        fontName='Helvetica-Oblique'
    ))
    
    return styles

def analyze_responses(comparison_data):
    """Perform detailed analysis of responses"""
    analysis = {
        'total_questions': len(comparison_data),
        'categories': {},
        'response_types': {},
        'confidence_distribution': {'high': 0, 'medium': 0, 'low': 0},
        'errors': 0,
        'perfect_matches': 0,
        'good_responses': 0,
        'average_confidence': 0,
        'category_performance': {}
    }
    
    total_confidence = 0
    
    for item in comparison_data:
        # Count categories
        category = item['category']
        if category not in analysis['categories']:
            analysis['categories'][category] = 0
        analysis['categories'][category] += 1
        
        # Count response types
        resp_type = item['response_type']
        if resp_type not in analysis['response_types']:
            analysis['response_types'][resp_type] = 0
        analysis['response_types'][resp_type] += 1
        
        # Confidence distribution
        confidence = item['confidence']
        total_confidence += confidence
        
        if confidence >= 0.8:
            analysis['confidence_distribution']['high'] += 1
        elif confidence >= 0.5:
            analysis['confidence_distribution']['medium'] += 1
        else:
            analysis['confidence_distribution']['low'] += 1
        
        # Error count
        if item['has_error']:
            analysis['errors'] += 1
        
        # Response quality assessment
        if confidence >= 0.9 and not item['has_error']:
            analysis['perfect_matches'] += 1
        elif confidence >= 0.7 and not item['has_error']:
            analysis['good_responses'] += 1
        
        # Category performance
        if category not in analysis['category_performance']:
            analysis['category_performance'][category] = {'total': 0, 'confidence_sum': 0, 'errors': 0}
        
        analysis['category_performance'][category]['total'] += 1
        analysis['category_performance'][category]['confidence_sum'] += confidence
        if item['has_error']:
            analysis['category_performance'][category]['errors'] += 1
    
    # Calculate averages
    analysis['average_confidence'] = total_confidence / len(comparison_data) if comparison_data else 0
    analysis['success_rate'] = ((analysis['total_questions'] - analysis['errors']) / analysis['total_questions'] * 100) if analysis['total_questions'] > 0 else 0
    
    # Calculate category averages
    for category, perf in analysis['category_performance'].items():
        perf['avg_confidence'] = perf['confidence_sum'] / perf['total'] if perf['total'] > 0 else 0
        perf['success_rate'] = ((perf['total'] - perf['errors']) / perf['total'] * 100) if perf['total'] > 0 else 0
    
    return analysis

def create_analysis_tables(analysis):
    """Create analysis tables"""
    tables = []
    
    # Overall performance table
    overall_data = [
        ['Metric', 'Value', 'Percentage'],
        ['Total Questions Tested', str(analysis['total_questions']), '100%'],
        ['Successful Responses', str(analysis['total_questions'] - analysis['errors']), f"{analysis['success_rate']:.1f}%"],
        ['Perfect Matches (≥90% confidence)', str(analysis['perfect_matches']), f"{(analysis['perfect_matches']/analysis['total_questions']*100):.1f}%"],
        ['Good Responses (≥70% confidence)', str(analysis['good_responses']), f"{(analysis['good_responses']/analysis['total_questions']*100):.1f}%"],
        ['Average Confidence Score', f"{analysis['average_confidence']:.3f}", f"{analysis['average_confidence']*100:.1f}%"],
        ['Errors Encountered', str(analysis['errors']), f"{(analysis['errors']/analysis['total_questions']*100):.1f}%"],
    ]
    
    overall_table = Table(overall_data, colWidths=[2.5*inch, 1.5*inch, 1*inch])
    overall_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
    ]))
    tables.append(('Overall Performance', overall_table))
    
    # Category performance table
    category_data = [['Category', 'Questions', 'Avg Confidence', 'Success Rate']]
    for category, perf in sorted(analysis['category_performance'].items()):
        category_data.append([
            category.replace('_', ' ').title(),
            str(perf['total']),
            f"{perf['avg_confidence']:.3f}",
            f"{perf['success_rate']:.1f}%"
        ])
    
    category_table = Table(category_data, colWidths=[2*inch, 1*inch, 1.5*inch, 1*inch])
    category_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 11),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('FONTSIZE', (0, 1), (-1, -1), 9),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
    ]))
    tables.append(('Category Performance', category_table))
    
    # Response type distribution
    response_data = [['Response Type', 'Count', 'Percentage']]
    for resp_type, count in sorted(analysis['response_types'].items()):
        percentage = (count / analysis['total_questions'] * 100)
        response_data.append([
            resp_type.replace('_', ' ').title(),
            str(count),
            f"{percentage:.1f}%"
        ])
    
    response_table = Table(response_data, colWidths=[2*inch, 1*inch, 1.5*inch])
    response_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.purple),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 11),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.lavender),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('FONTSIZE', (0, 1), (-1, -1), 9),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
    ]))
    tables.append(('Response Type Distribution', response_table))
    
    return tables

def create_enhanced_pdf(comparison_data, output_filename):
    """Create enhanced PDF with detailed analysis"""
    doc = SimpleDocTemplate(
        output_filename,
        pagesize=A4,
        rightMargin=50,
        leftMargin=50,
        topMargin=50,
        bottomMargin=30
    )
    
    styles = create_enhanced_styles()
    story = []
    
    # Title page
    story.append(Paragraph("MMMUT Admission Chatbot", styles['MainTitle']))
    story.append(Paragraph("Comprehensive Q&A Analysis Report", styles['Subtitle']))
    story.append(Spacer(1, 0.5*inch))
    
    # Metadata
    metadata = comparison_data.get('metadata', {})
    story.append(Paragraph(f"<b>Report Generated:</b> {datetime.now().strftime('%B %d, %Y at %I:%M %p')}", styles['Normal']))
    story.append(Paragraph(f"<b>Test Date:</b> {metadata.get('generation_date', 'Unknown')}", styles['Normal']))
    story.append(Paragraph(f"<b>Chatbot Version:</b> {metadata.get('chatbot_version', 'Unknown')}", styles['Normal']))
    story.append(Paragraph(f"<b>Total Questions Analyzed:</b> {metadata.get('total_questions', 0)}", styles['Normal']))
    story.append(Spacer(1, 0.3*inch))
    
    # Executive summary
    qa_data = comparison_data['comparison_data']
    analysis = analyze_responses(qa_data)
    
    story.append(Paragraph("Executive Summary", styles['SectionHeading']))
    summary_text = f"""
    This comprehensive analysis evaluates the performance of the MMMUT Admission Chatbot across {analysis['total_questions']} 
    carefully selected questions covering all major admission-related topics. The chatbot demonstrated exceptional performance 
    with a {analysis['success_rate']:.1f}% success rate and an average confidence score of {analysis['average_confidence']*100:.1f}%.
    
    <b>Key Highlights:</b><br/>
    • {analysis['perfect_matches']} questions ({(analysis['perfect_matches']/analysis['total_questions']*100):.1f}%) received perfect responses (≥90% confidence)<br/>
    • {analysis['good_responses']} questions ({(analysis['good_responses']/analysis['total_questions']*100):.1f}%) received good responses (≥70% confidence)<br/>
    • Only {analysis['errors']} errors encountered across all test cases<br/>
    • Covers {len(analysis['categories'])} different categories of admission queries
    """
    story.append(Paragraph(summary_text, styles['Normal']))
    story.append(PageBreak())
    
    # Analysis tables
    story.append(Paragraph("Detailed Performance Analysis", styles['SectionHeading']))
    tables = create_analysis_tables(analysis)
    
    for table_name, table in tables:
        story.append(Paragraph(table_name, styles['SectionHeading']))
        story.append(table)
        story.append(Spacer(1, 0.3*inch))
    
    story.append(PageBreak())
    
    # Detailed Q&A comparison
    story.append(Paragraph("Question & Answer Comparison", styles['SectionHeading']))
    story.append(Paragraph("The following section provides a detailed comparison between expected answers and actual chatbot responses for each test question.", styles['Normal']))
    story.append(Spacer(1, 0.2*inch))
    
    # Group questions by category for better organization
    categories = {}
    for item in qa_data:
        category = item['category']
        if category not in categories:
            categories[category] = []
        categories[category].append(item)
    
    question_number = 1
    for category, items in sorted(categories.items()):
        story.append(Paragraph(f"{category.replace('_', ' ').title()} Questions", styles['SectionHeading']))
        
        for item in items:
            # Question
            story.append(Paragraph(f"<b>Question {question_number}:</b> {item['question']}", styles['QuestionStyle']))
            
            # Expected answer
            story.append(Paragraph("<b>Expected Answer:</b>", styles['Normal']))
            story.append(Paragraph(item['expected_answer'], styles['ExpectedStyle']))
            
            # Chatbot response
            story.append(Paragraph("<b>Chatbot Response:</b>", styles['Normal']))
            story.append(Paragraph(item['chatbot_response'], styles['ChatbotStyle']))
            
            # Analysis
            analysis_text = f"<b>Analysis:</b> Response Type: {item['response_type']} | Confidence: {item['confidence']:.2f} | Source: {item['source']}"
            if item['sources']:
                analysis_text += f" | Knowledge Sources: {', '.join(item['sources'])}"
            if item['has_error']:
                analysis_text += " | ⚠️ Error occurred during processing"
            
            story.append(Paragraph(analysis_text, styles['AnalysisStyle']))
            story.append(Spacer(1, 0.2*inch))
            
            question_number += 1
        
        story.append(PageBreak())
    
    # Build PDF
    doc.build(story)
    return output_filename

def main():
    parser = argparse.ArgumentParser(description='Create enhanced PDF comparison document')
    parser.add_argument('json_file', help='Input JSON file with comparison data')
    parser.add_argument('-o', '--output', help='Output PDF filename', default=None)
    
    args = parser.parse_args()
    
    # Check input file
    if not Path(args.json_file).exists():
        print(f"❌ Input file not found: {args.json_file}")
        sys.exit(1)
    
    # Load data
    try:
        with open(args.json_file, 'r', encoding='utf-8') as f:
            comparison_data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading JSON file: {e}")
        sys.exit(1)
    
    # Generate output filename
    if args.output:
        output_filename = args.output
    else:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_filename = f"MMMUT_Enhanced_QA_Analysis_{timestamp}.pdf"
    
    # Create PDF
    try:
        print(f"📄 Creating enhanced PDF analysis...")
        create_enhanced_pdf(comparison_data, output_filename)
        print(f"✅ Enhanced PDF created: {output_filename}")
        
        # Analysis summary
        qa_data = comparison_data['comparison_data']
        analysis = analyze_responses(qa_data)
        
        print(f"\n📊 Analysis Summary:")
        print(f"   Total Questions: {analysis['total_questions']}")
        print(f"   Success Rate: {analysis['success_rate']:.1f}%")
        print(f"   Average Confidence: {analysis['average_confidence']*100:.1f}%")
        print(f"   Perfect Responses: {analysis['perfect_matches']} ({(analysis['perfect_matches']/analysis['total_questions']*100):.1f}%)")
        print(f"   Categories Covered: {len(analysis['categories'])}")
        print(f"   PDF Size: {Path(output_filename).stat().st_size / 1024:.1f} KB")
        
    except Exception as e:
        print(f"❌ Error creating enhanced PDF: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
