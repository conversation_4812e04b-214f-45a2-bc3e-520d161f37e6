#!/usr/bin/env python3
"""
Generate Q&A comparison document for MMMUT Chatbot
This script tests the chatbot with standard questions and generates a comparison PDF
"""

import sys
import os
import json
import requests
import time
from datetime import datetime
from pathlib import Path

# Add src directory to path
sys.path.append('src')

def test_chatbot_api(query, session_id=None):
    """Test the chatbot API with a query"""
    try:
        url = "http://localhost:8080/api/chat"
        payload = {
            "query": query,
            "session_id": session_id
        }
        
        response = requests.post(url, json=payload, timeout=30)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}", "response": "Error occurred"}
    except Exception as e:
        return {"error": str(e), "response": "Connection error"}

def load_standard_questions():
    """Load standard questions from data files"""
    standard_qa = []
    
    # Load from organized_data.json
    try:
        with open('data/organized_data.json', 'r', encoding='utf-8') as f:
            organized_data = json.load(f)
            
        # Extract FAQs
        faqs = organized_data.get('faq', [])
        for faq in faqs:
            standard_qa.append({
                'question': faq['question'],
                'expected_answer': faq['answer'],
                'category': faq['category'],
                'source': 'organized_data.json'
            })
    except Exception as e:
        print(f"Error loading organized_data.json: {e}")
    
    # Load from structured_data.json
    try:
        with open('data/structured_data.json', 'r', encoding='utf-8') as f:
            structured_data = json.load(f)
            
        # Extract FAQs
        faqs = structured_data.get('faq', [])
        for faq in faqs:
            standard_qa.append({
                'question': faq['question'],
                'expected_answer': faq['answer'],
                'category': faq['category'],
                'source': 'structured_data.json'
            })
    except Exception as e:
        print(f"Error loading structured_data.json: {e}")
    
    # Load from PDF knowledge
    try:
        with open('data/pdf_structured_knowledge.json', 'r', encoding='utf-8') as f:
            pdf_data = json.load(f)
            
        # Extract BBA FAQs
        if 'bba' in pdf_data:
            bba_faqs = pdf_data['bba'].get('faqs', [])
            for faq in bba_faqs:
                standard_qa.append({
                    'question': faq['question'],
                    'expected_answer': faq['answer'],
                    'category': faq['category'],
                    'source': 'PDF - BBA'
                })
        
        # Extract B.Pharma FAQs
        if 'bpharma' in pdf_data:
            bpharma_faqs = pdf_data['bpharma'].get('faqs', [])
            for faq in bpharma_faqs:
                standard_qa.append({
                    'question': faq['question'],
                    'expected_answer': faq['answer'],
                    'category': faq['category'],
                    'source': 'PDF - B.Pharma'
                })
    except Exception as e:
        print(f"Error loading PDF knowledge: {e}")
    
    # Add additional test questions
    additional_questions = [
        {
            'question': 'Hello, I need help with MMMUT admissions',
            'expected_answer': 'Greeting response with welcome message',
            'category': 'greeting',
            'source': 'test_case'
        },
        {
            'question': 'What engineering courses are available at MMMUT?',
            'expected_answer': 'List of B.Tech engineering branches offered',
            'category': 'courses',
            'source': 'test_case'
        },
        {
            'question': 'What is the complete fee structure for B.Tech?',
            'expected_answer': 'Detailed breakdown of B.Tech fees including tuition, hostel, mess',
            'category': 'fees',
            'source': 'test_case'
        },
        {
            'question': 'When are the important admission dates for 2024?',
            'expected_answer': 'Timeline for JEE Main and CUET applications and exams',
            'category': 'important_dates',
            'source': 'test_case'
        },
        {
            'question': 'What are the placement statistics of MMMUT?',
            'expected_answer': 'Placement percentage, average package, top recruiters',
            'category': 'placement',
            'source': 'test_case'
        },
        {
            'question': 'How can I contact the admission office?',
            'expected_answer': 'Phone number, email, address of admission office',
            'category': 'contact',
            'source': 'test_case'
        },
        {
            'question': 'Tell me about campus facilities and hostel accommodation',
            'expected_answer': 'Information about academic facilities, hostels, library, labs',
            'category': 'facilities',
            'source': 'test_case'
        },
        {
            'question': 'What is CUET and how is it related to MMMUT admissions?',
            'expected_answer': 'Explanation of CUET exam and its role in BBA/B.Pharma admissions',
            'category': 'entrance_exam',
            'source': 'test_case'
        }
    ]
    
    standard_qa.extend(additional_questions)
    return standard_qa

def generate_comparison_data():
    """Generate comparison data by testing chatbot"""
    print("🚀 Generating Q&A Comparison Data...")
    print("=" * 60)
    
    # Load standard questions
    standard_qa = load_standard_questions()
    print(f"📋 Loaded {len(standard_qa)} standard questions")
    
    # Test each question
    comparison_data = []
    session_id = f"comparison_test_{int(time.time())}"
    
    for i, qa in enumerate(standard_qa, 1):
        print(f"\n🔍 Testing Question {i}/{len(standard_qa)}")
        print(f"Category: {qa['category']}")
        print(f"Question: {qa['question'][:100]}...")
        
        # Get chatbot response
        chatbot_response = test_chatbot_api(qa['question'], session_id)
        
        # Prepare comparison entry
        comparison_entry = {
            'question_number': i,
            'question': qa['question'],
            'category': qa['category'],
            'source': qa['source'],
            'expected_answer': qa['expected_answer'],
            'chatbot_response': chatbot_response.get('response', 'No response'),
            'response_type': chatbot_response.get('response_type', 'unknown'),
            'confidence': chatbot_response.get('confidence', 0),
            'sources': chatbot_response.get('sources', []),
            'timestamp': datetime.now().isoformat(),
            'has_error': 'error' in chatbot_response
        }
        
        comparison_data.append(comparison_entry)
        
        # Brief pause to avoid overwhelming the API
        time.sleep(0.5)
        
        print(f"✅ Response received (Type: {comparison_entry['response_type']}, Confidence: {comparison_entry['confidence']})")
    
    return comparison_data

def save_comparison_data(comparison_data):
    """Save comparison data to JSON file"""
    output_file = f"qa_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'metadata': {
                'generation_date': datetime.now().isoformat(),
                'total_questions': len(comparison_data),
                'chatbot_version': 'MMMUT Admission Chatbot v2.0',
                'test_session': 'Automated Q&A Comparison'
            },
            'comparison_data': comparison_data
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Comparison data saved to: {output_file}")
    return output_file

if __name__ == "__main__":
    try:
        # Check if chatbot is running
        health_check = requests.get("http://localhost:8080/api/health", timeout=5)
        if health_check.status_code != 200:
            print("❌ Chatbot is not running. Please start it first with: python3 app.py")
            sys.exit(1)
        
        print("✅ Chatbot is running and accessible")
        
        # Generate comparison data
        comparison_data = generate_comparison_data()
        
        # Save to file
        output_file = save_comparison_data(comparison_data)
        
        print(f"\n🎉 Q&A Comparison generation completed!")
        print(f"📊 Total questions tested: {len(comparison_data)}")
        print(f"📁 Output file: {output_file}")
        print(f"\nNext step: Run 'python3 create_comparison_pdf.py {output_file}' to generate PDF")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to chatbot. Please ensure it's running on http://localhost:8080")
        print("Start the chatbot with: python3 app.py")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
