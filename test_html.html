<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test JavaScript</title>
</head>
<body>
    <div class="quick-suggestions">
        <div class="suggestion-chip" onclick="sendSuggestion('What engineering courses are available at MMMUT?')">📚 Available Courses</div>
        <div class="suggestion-chip" onclick="sendSuggestion('What are the eligibility criteria for B.Tech admission?')">✅ Eligibility Criteria</div>
    </div>
    
    <button class="send-button" id="sendButton" onclick="sendMessage()">
        <i class="fas fa-paper-plane"></i>
    </button>

    <script>
        // Test functions
        function sendMessage() {
            console.log('sendMessage called');
        }
        
        function sendSuggestion(suggestion) {
            console.log('sendSuggestion called with:', suggestion);
        }
        
        // Make functions globally accessible
        window.sendMessage = sendMessage;
        window.sendSuggestion = sendSuggestion;
    </script>
</body>
</html>
