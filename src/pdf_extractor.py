"""
PDF Content Extractor for MMMUT Admission Documents
Extracts and processes text content from PDF files for chatbot knowledge base
"""

import os
import json
import re
from typing import Dict, List, Any
import PyPDF2
import fitz  # PyMuPDF - better for text extraction
from pathlib import Path

class PDFExtractor:
    def __init__(self, pdf_directory: str = "data/raw_data"):
        self.pdf_directory = Path(pdf_directory)
        self.extracted_data = {}
        
    def extract_all_pdfs(self) -> Dict[str, Any]:
        """Extract content from all PDF files in the directory"""
        pdf_files = list(self.pdf_directory.glob("*.pdf"))
        
        for pdf_file in pdf_files:
            print(f"Extracting content from: {pdf_file.name}")
            content = self.extract_pdf_content(pdf_file)
            self.extracted_data[pdf_file.stem] = content
            
        return self.extracted_data
    
    def extract_pdf_content(self, pdf_path: Path) -> Dict[str, Any]:
        """Extract structured content from a single PDF"""
        try:
            # Try PyMuPDF first (better text extraction)
            doc = fitz.open(str(pdf_path))
            text_content = ""
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text_content += page.get_text()
                
            doc.close()
            
            # Process and structure the extracted text
            structured_content = self.process_extracted_text(text_content, pdf_path.stem)
            return structured_content
            
        except Exception as e:
            print(f"Error extracting {pdf_path.name} with PyMuPDF: {e}")
            # Fallback to PyPDF2
            return self.extract_with_pypdf2(pdf_path)
    
    def extract_with_pypdf2(self, pdf_path: Path) -> Dict[str, Any]:
        """Fallback extraction using PyPDF2"""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_content = ""
                
                for page in pdf_reader.pages:
                    text_content += page.extract_text()
                
            structured_content = self.process_extracted_text(text_content, pdf_path.stem)
            return structured_content
            
        except Exception as e:
            print(f"Error extracting {pdf_path.name}: {e}")
            return {"error": str(e), "raw_text": ""}
    
    def process_extracted_text(self, text: str, filename: str) -> Dict[str, Any]:
        """Process and structure extracted text based on document type"""
        
        # Clean the text
        text = self.clean_text(text)
        
        # Determine document type and extract relevant information
        if "bpharma" in filename.lower() or "pharma" in filename.lower():
            return self.extract_bpharma_info(text)
        elif "btech" in filename.lower() or "engineering" in filename.lower():
            return self.extract_btech_info(text)
        elif "brochure" in filename.lower() or "admission" in filename.lower():
            return self.extract_admission_brochure_info(text)
        else:
            return self.extract_general_info(text)
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove special characters that might interfere
        text = re.sub(r'[^\w\s\-.,()%₹:/]', ' ', text)
        # Normalize spacing
        text = text.strip()
        return text
    
    def extract_bpharma_info(self, text: str) -> Dict[str, Any]:
        """Extract B.Pharma specific information"""
        info = {
            "course_type": "B.Pharma",
            "raw_text": text,
            "extracted_info": {}
        }
        
        # Extract eligibility criteria
        eligibility_patterns = [
            r'eligibility[:\s]*([^.]*(?:10\+2|12th|intermediate)[^.]*)',
            r'qualification[:\s]*([^.]*(?:10\+2|12th|intermediate)[^.]*)',
            r'admission criteria[:\s]*([^.]*(?:10\+2|12th|intermediate)[^.]*)'
        ]
        
        for pattern in eligibility_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                info["extracted_info"]["eligibility"] = matches[0].strip()
                break
        
        # Extract fee information
        fee_patterns = [
            r'fee[s]?[:\s]*[₹]?(\d+[,\d]*)',
            r'cost[:\s]*[₹]?(\d+[,\d]*)',
            r'tuition[:\s]*[₹]?(\d+[,\d]*)'
        ]
        
        fees = []
        for pattern in fee_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            fees.extend(matches)
        
        if fees:
            info["extracted_info"]["fees"] = list(set(fees))
        
        # Extract duration
        duration_patterns = [
            r'duration[:\s]*(\d+\s*years?)',
            r'(\d+)\s*years?\s*course',
            r'(\d+)\s*years?\s*program'
        ]
        
        for pattern in duration_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                info["extracted_info"]["duration"] = matches[0]
                break
        
        # Extract entrance exam info
        if "cuet" in text.lower():
            info["extracted_info"]["entrance_exam"] = "CUET"
        elif "neet" in text.lower():
            info["extracted_info"]["entrance_exam"] = "NEET"
        
        return info
    
    def extract_btech_info(self, text: str) -> Dict[str, Any]:
        """Extract B.Tech specific information"""
        info = {
            "course_type": "B.Tech",
            "raw_text": text,
            "extracted_info": {}
        }
        
        # Extract branches/specializations
        branch_patterns = [
            r'computer science',
            r'mechanical engineering',
            r'civil engineering',
            r'electrical engineering',
            r'electronics',
            r'information technology',
            r'chemical engineering',
            r'biotechnology'
        ]
        
        branches = []
        for pattern in branch_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                branches.append(pattern.title())
        
        if branches:
            info["extracted_info"]["branches"] = branches
        
        # Extract JEE Main information
        if "jee main" in text.lower() or "jee" in text.lower():
            info["extracted_info"]["entrance_exam"] = "JEE Main"
        
        # Extract eligibility
        if "75%" in text or "75 percent" in text:
            info["extracted_info"]["minimum_marks"] = "75%"
        
        return info
    
    def extract_admission_brochure_info(self, text: str) -> Dict[str, Any]:
        """Extract information from admission brochure"""
        info = {
            "document_type": "Admission Brochure",
            "raw_text": text,
            "extracted_info": {}
        }
        
        # Extract important dates
        date_patterns = [
            r'application[:\s]*([^.]*\d{1,2}[/-]\d{1,2}[/-]\d{4}[^.]*)',
            r'last date[:\s]*([^.]*\d{1,2}[/-]\d{1,2}[/-]\d{4}[^.]*)',
            r'deadline[:\s]*([^.]*\d{1,2}[/-]\d{1,2}[/-]\d{4}[^.]*)'
        ]
        
        dates = []
        for pattern in date_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            dates.extend(matches)
        
        if dates:
            info["extracted_info"]["important_dates"] = dates
        
        # Extract contact information
        contact_patterns = [
            r'phone[:\s]*([+\d\s-]+)',
            r'email[:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
            r'website[:\s]*([www\.]?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
        ]
        
        contacts = {}
        for i, pattern in enumerate(contact_patterns):
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                contact_type = ['phone', 'email', 'website'][i]
                contacts[contact_type] = matches[0].strip()
        
        if contacts:
            info["extracted_info"]["contact"] = contacts
        
        return info
    
    def extract_general_info(self, text: str) -> Dict[str, Any]:
        """Extract general information from any document"""
        info = {
            "document_type": "General",
            "raw_text": text,
            "extracted_info": {}
        }
        
        # Extract any numerical information (fees, percentages, etc.)
        numbers = re.findall(r'₹?\d+[,\d]*', text)
        if numbers:
            info["extracted_info"]["numbers_found"] = numbers[:10]  # Limit to first 10
        
        # Extract any dates
        dates = re.findall(r'\d{1,2}[/-]\d{1,2}[/-]\d{4}', text)
        if dates:
            info["extracted_info"]["dates_found"] = dates
        
        return info
    
    def save_extracted_data(self, output_file: str = "data/pdf_extracted_data.json"):
        """Save extracted data to JSON file"""
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, indent=2, ensure_ascii=False)
        
        print(f"Extracted data saved to: {output_path}")
    
    def get_course_specific_data(self, course_type: str) -> Dict[str, Any]:
        """Get extracted data for a specific course type"""
        course_data = {}
        
        for filename, data in self.extracted_data.items():
            if data.get("course_type", "").lower() == course_type.lower():
                course_data[filename] = data
        
        return course_data

if __name__ == "__main__":
    # Test the extractor
    extractor = PDFExtractor()
    extracted_data = extractor.extract_all_pdfs()
    extractor.save_extracted_data()
    
    print("\nExtraction Summary:")
    for filename, data in extracted_data.items():
        print(f"- {filename}: {data.get('course_type', 'General')} document")
        if 'extracted_info' in data:
            print(f"  Extracted fields: {list(data['extracted_info'].keys())}")