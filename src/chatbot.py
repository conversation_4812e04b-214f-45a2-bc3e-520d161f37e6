"""
Main chatbot module using Google Gemini AI for MMMUT admission queries
"""

import json
import logging
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import google.generativeai as genai
import sys
import os

# Add the src directory to the path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from pdf_knowledge_integrator import PDFKnowledgeIntegrator
except ImportError:
    PDFKnowledgeIntegrator = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdmissionChatbot:
    """MMMUT Admission Chatbot using Google Gemini AI"""
    
    def __init__(self):
        """Initialize the chatbot"""
        self._setup_gemini()
        self._load_data()
        self._load_pdf_knowledge()
        self._setup_conversation_history()
        
        logger.info("MMMUT Admission Chatbot initialized successfully")
    
    def _setup_gemini(self):
        """Setup Google Gemini AI"""
        try:
            from config.settings import GEMINI_API_KEY
            from config.chatbot_config import (
                GEMINI_MODEL, GENERATION_CONFIG, SAFETY_SETTINGS, SYSTEM_PROMPT
            )
            
            # Configure Gemini
            genai.configure(api_key=GEMINI_API_KEY)
            
            # Initialize the model
            self.model = genai.GenerativeModel(
                model_name=GEMINI_MODEL,
                generation_config=GENERATION_CONFIG,
                safety_settings=SAFETY_SETTINGS
            )
            
            # Start chat session with system prompt
            self.chat = self.model.start_chat(history=[])
            self.system_prompt = SYSTEM_PROMPT
            
            logger.info("Gemini AI configured successfully")
            
        except Exception as e:
            logger.error(f"Error setting up Gemini AI: {str(e)}")
            raise
    
    def _load_data(self):
        """Load organized admission data"""
        try:
            from config.settings import DATA_DIR
            
            # Try to load organized data first
            organized_data_path = DATA_DIR / "organized_data.json"
            if organized_data_path.exists():
                with open(organized_data_path, 'r', encoding='utf-8') as f:
                    self.organized_data = json.load(f)
            else:
                # Fallback to structured data
                structured_data_path = DATA_DIR / "structured_data.json"
                with open(structured_data_path, 'r', encoding='utf-8') as f:
                    raw_data = json.load(f)
                
                # Create basic organized structure
                self.organized_data = self._create_basic_organized_data(raw_data)
            
            # Load quick responses
            self.quick_responses = self.organized_data.get("quick_responses", {})
            self.faqs = self.organized_data.get("faq", [])
            
            logger.info("Admission data loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            # Create minimal fallback data
            self._create_fallback_data()
    
    def _create_basic_organized_data(self, raw_data: Dict) -> Dict:
        """Create basic organized data structure from raw data"""
        return {
            "categories": {
                "university": {"data": raw_data.get("university_info", {})},
                "courses": {"data": {"undergraduate": {"engineering": raw_data.get("courses", [])}}},
                "eligibility": {"data": raw_data.get("eligibility", {})},
                "fees": {"data": raw_data.get("fees", {})},
                "important_dates": {"data": raw_data.get("important_dates", {})},
                "facilities": {"data": raw_data.get("facilities", [])},
                "placement": {"data": raw_data.get("placement_info", {})},
                "contact": {"data": raw_data.get("contact_info", {})}
            },
            "quick_responses": {
                "greeting": "Hello! Welcome to MMMUT Admission Help Desk. How can I assist you today?",
                "fallback": "I'm sorry, I don't have specific information about that. Could you please ask about courses, eligibility, fees, or other admission-related topics?"
            },
            "faq": []
        }
    
    def _create_fallback_data(self):
        """Create minimal fallback data when no data files are available"""
        self.organized_data = {
            "categories": {
                "university": {
                    "data": {
                        "name": "Madan Mohan Malaviya University of Technology",
                        "location": "Gorakhpur, Uttar Pradesh"
                    }
                }
            },
            "quick_responses": {
                "greeting": "Hello! Welcome to MMMUT Admission Help Desk. How can I assist you today?",
                "fallback": "I'm sorry, I'm experiencing some technical difficulties. Please try again later or contact the admission office directly."
            },
            "faq": []
        }
        self.quick_responses = self.organized_data["quick_responses"]
        self.faqs = self.organized_data["faq"]
    
    def _load_pdf_knowledge(self):
        """Load knowledge from PDF documents"""
        self.pdf_knowledge = {}
        if PDFKnowledgeIntegrator:
            try:
                integrator = PDFKnowledgeIntegrator()
                self.pdf_knowledge = integrator.process_all_documents()
                
                # Enhance FAQs with PDF content
                pdf_faqs = integrator.create_enhanced_faqs()
                self.faqs.extend(pdf_faqs)
                
                logger.info(f"PDF knowledge loaded: {len(self.pdf_knowledge)} documents, {len(pdf_faqs)} additional FAQs")
            except Exception as e:
                logger.warning(f"Could not load PDF knowledge: {e}")
                self.pdf_knowledge = {}
        else:
            logger.warning("PDFKnowledgeIntegrator not available")
    
    def _setup_conversation_history(self):
        """Setup conversation history tracking"""
        self.conversation_history = []
        self.session_start_time = datetime.now()
        self.query_count = 0
    
    def process_query(self, user_query: str, user_id: str = None) -> Dict[str, Any]:
        """Process user query and return response"""
        try:
            self.query_count += 1
            
            # Log the query
            logger.info(f"Processing query: {user_query[:100]}...")
            
            # Preprocess the query
            processed_query = self._preprocess_query(user_query)
            
            # Check for quick responses
            quick_response = self._check_quick_responses(processed_query)
            if quick_response:
                response_data = {
                    "response": quick_response,
                    "response_type": "quick",
                    "confidence": 0.9,
                    "sources": ["quick_responses"],
                    "timestamp": datetime.now().isoformat()
                }
            else:
                # Generate AI response
                response_data = self._generate_ai_response(processed_query)
            
            # Add to conversation history
            self._add_to_history(user_query, response_data["response"])
            
            # Add metadata
            response_data.update({
                "query_id": f"q_{self.query_count}_{int(datetime.now().timestamp())}",
                "user_id": user_id,
                "session_duration": str(datetime.now() - self.session_start_time)
            })
            
            return response_data
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return self._create_error_response(str(e))
    
    def _preprocess_query(self, query: str) -> str:
        """Enhanced preprocessing for better query understanding"""
        # Basic cleaning
        query = query.strip()
        if not query:
            return query

        # Preserve original case for proper nouns but create lowercase version for processing
        original_query = query
        query_lower = query.lower()

        # Remove extra spaces and normalize punctuation
        query_lower = re.sub(r'\s+', ' ', query_lower)
        query_lower = re.sub(r'[^\w\s\-\.]', ' ', query_lower)

        # Handle common abbreviations and expansions
        abbreviations = {
            'cse': 'computer science engineering',
            'ece': 'electronics and communication engineering',
            'eee': 'electrical and electronics engineering',
            'ee': 'electrical engineering',
            'me': 'mechanical engineering',
            'ce': 'civil engineering',
            'it': 'information technology',
            'btech': 'bachelor of technology',
            'b.tech': 'bachelor of technology',
            'mtech': 'master of technology',
            'm.tech': 'master of technology',
            'phd': 'doctor of philosophy',
            'ph.d': 'doctor of philosophy',
            'mmmut': 'madan mohan malaviya university of technology',
            'gorakhpur': 'gorakhpur uttar pradesh',
            'up': 'uttar pradesh'
        }

        # Apply abbreviation expansions
        for abbr, full_form in abbreviations.items():
            query_lower = re.sub(r'\b' + re.escape(abbr) + r'\b', full_form, query_lower)

        # Handle common question patterns
        question_patterns = {
            r'\bwhat\s+is\s+the\s+': 'tell me about the ',
            r'\bhow\s+much\s+': 'what is the cost of ',
            r'\bwhen\s+is\s+': 'what are the dates for ',
            r'\bwhere\s+is\s+': 'what is the location of ',
            r'\bcan\s+i\s+': 'am i eligible for ',
            r'\bdo\s+you\s+have\s+': 'does mmmut offer '
        }

        for pattern, replacement in question_patterns.items():
            query_lower = re.sub(pattern, replacement, query_lower)

        # Return processed query while preserving some original formatting
        return query_lower
    
    def _check_quick_responses(self, query: str) -> Optional[str]:
        """Check if query matches any quick response patterns with enhanced specificity"""
        query_lower = query.lower()
        
        # Enhanced greeting patterns
        greeting_patterns = [
            'hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening',
            'namaste', 'greetings', 'start', 'begin', 'help me'
        ]
        if any(pattern in query_lower for pattern in greeting_patterns):
            greetings = self.quick_responses.get("greeting", [])
            if isinstance(greetings, list):
                import random
                return random.choice(greetings)
            return greetings or "🎓 Hello! Welcome to MMMUT Admission Help Desk. I'm here to provide you with specific, detailed information about MMMUT admissions. What would you like to know?"

        # Enhanced category-specific patterns with priority scoring
        category_patterns = {
            'courses': {
                'patterns': ['course', 'program', 'branch', 'stream', 'what courses', 'engineering', 'btech', 'b.tech', 'degree', 'specialization', 'department', 'bba', 'b.pharma', 'pharmacy', 'management'],
                'response': """🎓 **MMMUT Courses Offered:**

**🔧 B.Tech Engineering (4 years) - JEE Main:**
• Computer Science and Engineering (CSE)
• Information Technology (IT)
• Electronics and Communication Engineering (ECE)
• Electrical Engineering (EE)
• Mechanical Engineering (ME)
• Civil Engineering (CE)
• Chemical Engineering
• Biotechnology
• **Annual Fee:** ₹1,00,000

**💼 BBA (3 years) - CUET:**
• Bachelor of Business Administration
• **Annual Fee:** ₹90,000
• **Eligibility:** 10+2 any stream with 50%

**💊 B.Pharma (4 years) - CUET:**
• Bachelor of Pharmacy
• **Annual Fee:** ₹1,10,000
• **Eligibility:** 10+2 with PCB/PCM and 50%

📞 **Need specific course details?** Call +91-551-2273958 <NAME_EMAIL>

Which specific course interests you most?"""
            },
            'eligibility': {
                'patterns': ['eligibility', 'criteria', 'qualification', 'requirement', 'marks', 'percentage', 'cutoff', 'cut off', 'minimum marks', 'qualify', 'cuet', 'jee'],
                'response': """📋 **MMMUT Eligibility Criteria:**

**🎓 B.Tech Engineering:**
• 10+2 with Physics, Chemistry, Mathematics (PCM)
• **Minimum 75% marks** in 10+2
• **JEE Main qualification** required
• No age limit

**💼 BBA:**
• 10+2 from any stream (Science/Commerce/Arts)
• **Minimum 50% marks** in 10+2
• **CUET qualification** required
• No age limit

**💊 B.Pharma:**
• 10+2 with Physics, Chemistry, Biology/Mathematics (PCB/PCM)
• **Minimum 50% marks** in 10+2
• **CUET qualification** required
• No age limit

**🌍 Nationality:** Indian/NRI/Foreign nationals eligible

📞 **Questions about your eligibility?** Call +91-551-2273958

Which course are you interested in?"""
            },
            'fees': {
                'patterns': ['fee', 'cost', 'payment', 'how much', 'price', 'tuition', 'scholarship', 'financial aid', 'installment', 'money'],
                'response': """💰 **MMMUT Fee Structure:**

**🎓 B.Tech Engineering (Annual):**
• Tuition: ₹50,000 | Hostel: ₹25,000 | Mess: ₹20,000 | Other: ₹5,000
• **Total Annual: ₹1,00,000**
• **Total Course (4 years): ₹4,00,000**

**💼 BBA (Annual):**
• Tuition: ₹40,000 | Hostel: ₹25,000 | Mess: ₹20,000 | Other: ₹5,000
• **Total Annual: ₹90,000**
• **Total Course (3 years): ₹2,70,000**

**💊 B.Pharma (Annual):**
• Tuition: ₹60,000 | Hostel: ₹25,000 | Mess: ₹20,000 | Other: ₹5,000
• **Total Annual: ₹1,10,000**
• **Total Course (4 years): ₹4,40,000**

**💳 Payment Options:**
• Online payment, Demand Draft, Bank Transfer
• 2 installments per year available
• Scholarships for meritorious students

📞 **Need payment assistance?** Call +91-551-2273958

Which course fee details do you need?"""
            },
            'dates': {
                'patterns': ['date', 'deadline', 'when', 'schedule', 'timeline', 'last date', 'application date', 'admission date', 'important dates', 'cuet', 'jee'],
                'response': """📅 **MMMUT Admission Timeline 2024:**

**🎓 JEE Main (B.Tech Engineering):**
• Application: December 1, 2023 - January 31, 2024
• Exam: January-April 2024 (2 sessions)
• Counseling: June 1, 2024
• Classes Start: August 15, 2024

**💼💊 CUET (BBA & B.Pharma):**
• Application: February 15 - March 31, 2024
• Exam: May 15-31, 2024
• Result: June 30, 2024
• Counseling: July 15, 2024
• Classes Start: September 1, 2024

**⚠️ Important Deadlines:**
• JEE Main Application: January 31, 2024
• CUET Application: March 31, 2024
• Fee Payment: Within 7 days of seat allotment

📞 **Need help with application?** Call +91-551-2273958

Which exam timeline interests you?"""
            },
            'contact': {
                'patterns': ['contact', 'phone', 'email', 'address', 'reach', 'office', 'helpline', 'support', 'call', 'write'],
                'response': """📞 **MMMUT Admission Office Contact:**

**Direct Contact:**
• Phone: +91-551-2273958
• Email: <EMAIL>
• Address: MMMUT, Gorakhpur - 273010, Uttar Pradesh

**Office Hours:**
• Monday to Friday: 9:00 AM to 5:00 PM
• Website: https://www.mmmut.ac.in

**Quick Help:**
• For urgent queries: Call the phone number
• For detailed queries: Send email
• For forms: Visit the website

🤝 **They're ready to help with your specific questions!**

What specific information do you need from them?"""
            },
            'facilities': {
                'patterns': ['facility', 'hostel', 'library', 'lab', 'infrastructure', 'campus', 'accommodation', 'mess', 'wifi', 'sports'],
                'response': """🏫 **MMMUT Campus Facilities:**

**Academic Facilities:**
• Central Library with 50,000+ books
• Computer Labs with latest equipment
• Well-equipped laboratories
• Smart classrooms

**Residential Facilities:**
• Separate Boys and Girls Hostels
• Mess facilities with quality food
• 24/7 security

**Recreational Facilities:**
• Sports Complex
• Gymnasium
• Auditorium
• Playground

**Other Amenities:**
• WiFi campus
• Medical facilities
• Transportation

📞 **Want to visit the campus?** Call +91-551-2273958

Which facility interests you most?"""
            },
            'placement': {
                'patterns': ['placement', 'job', 'career', 'salary', 'package', 'company', 'recruitment', 'internship', 'employment'],
                'response': """🎯 **MMMUT Placement Statistics:**

**Impressive Numbers:**
• **Placement Rate:** 85%+
• **Average Package:** ₹6.5 LPA
• **Highest Package:** ₹25 LPA

**Top Recruiters:**
• TCS, Infosys, Wipro, Accenture, IBM
• Microsoft, Amazon, Google, Flipkart
• And many more leading companies

**Support Services:**
• Active placement cell
• Soft skills and technical training
• Internship opportunities
• Career guidance

🚀 **Excellent career prospects await you!**

📞 **Want placement details for specific courses?** Call +91-551-2273958

Which aspect of placements interests you most?"""
            }
        }

        # Score-based matching for better accuracy
        best_match = None
        best_score = 0

        for category, data in category_patterns.items():
            patterns = data['patterns']
            score = sum(2 if pattern in query_lower else 0 for pattern in patterns)
            if score > best_score:
                best_score = score
                best_match = category

        if best_match and best_score > 0:
            return category_patterns[best_match]['response']

        # Check for location queries
        if any(word in query_lower for word in ['where', 'location', 'address', 'situated', 'gorakhpur', 'how to reach', 'directions']):
            return """📍 **MMMUT Location:**

**Address:**
Madan Mohan Malaviya University of Technology
Gorakhpur - 273010, Uttar Pradesh, India

**How to Reach:**
• **By Air:** Nearest airport - Gorakhpur Airport
• **By Train:** Gorakhpur Railway Station (well connected)
• **By Road:** Good connectivity via NH-28

**Local Transport:**
• Auto-rickshaws and buses available
• University provides transportation

📞 **Need detailed directions?** Call +91-551-2273958

Are you planning to visit the campus?"""

        return None
    
    def _generate_ai_response(self, query: str) -> Dict[str, Any]:
        """Generate response using Gemini AI"""
        try:
            # Create context from organized data
            context = self._create_context_for_query(query)
            
            # Prepare the prompt
            prompt = self._create_prompt(query, context)
            
            # Generate response using Gemini
            response = self.chat.send_message(prompt)
            
            # Process the response
            ai_response = response.text.strip()
            
            return {
                "response": ai_response,
                "response_type": "ai_generated",
                "confidence": 0.8,
                "sources": ["gemini_ai", "admission_data"],
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating AI response: {str(e)}")
            return {
                "response": self.quick_responses.get("fallback", "I'm sorry, I'm having trouble processing your request right now."),
                "response_type": "fallback",
                "confidence": 0.1,
                "sources": ["fallback"],
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def _create_context_for_query(self, query: str) -> str:
        """Create highly relevant and structured context from organized data for the query"""
        context_parts = []
        
        # Always include university basic info for context
        university_data = self.organized_data.get("categories", {}).get("university", {}).get("data", {})
        if university_data:
            context_parts.append(f"""
🏛️ **MMMUT UNIVERSITY INFO:**
• Full Name: {university_data.get('full_name', 'MMMUT')}
• Location: {university_data.get('location', 'Gorakhpur, UP')}
• Established: {university_data.get('established', '1962')}
• Type: {university_data.get('type', 'Government University')}
• Accreditation: {university_data.get('accreditation', 'NAAC Accredited')}
""")
        
        # Determine relevant categories with priority scoring
        relevant_categories = self._identify_relevant_categories_with_priority(query)
        
        # Add category-specific information with better formatting
        for category, priority in relevant_categories:
            category_data = self.organized_data.get("categories", {}).get(category, {}).get("data", {})
            if category_data:
                formatted_data = self._format_category_data(category, category_data)
                context_parts.append(formatted_data)
        
        # Add PDF-based course-specific information
        pdf_context = self._get_pdf_context(query)
        if pdf_context:
            context_parts.append(pdf_context)
        
        # Add highly relevant FAQs (including PDF FAQs)
        relevant_faqs = self._find_relevant_faqs(query)
        if relevant_faqs:
            faq_text = "\n🔍 **RELEVANT FAQs:**\n"
            for i, faq in enumerate(relevant_faqs[:3], 1):
                source = faq.get('source', 'General')
                faq_text += f"\n**Q{i}:** {faq['question']}\n**A{i}:** {faq['answer']}\n*Source: {source}*\n"
            context_parts.append(faq_text)
        
        # Add conversation context if available
        if self.conversation_history:
            recent_context = self._get_recent_conversation_context()
            if recent_context:
                context_parts.append(f"\n💬 **CONVERSATION CONTEXT:**\n{recent_context}")
        
        return "\n".join(context_parts)
    
    def _get_pdf_context(self, query: str) -> str:
        """Get relevant context from PDF knowledge based on query"""
        if not self.pdf_knowledge:
            return ""
        
        query_lower = query.lower()
        pdf_context_parts = []
        
        # Check for course-specific queries
        if any(word in query_lower for word in ['bba', 'business administration', 'management']):
            bba_info = self.pdf_knowledge.get('bba', {})
            if bba_info:
                pdf_context_parts.append(self._format_bba_context(bba_info, query_lower))
        
        if any(word in query_lower for word in ['b.pharma', 'pharma', 'pharmacy']):
            bpharma_info = self.pdf_knowledge.get('bpharma', {})
            if bpharma_info:
                pdf_context_parts.append(self._format_bpharma_context(bpharma_info, query_lower))
        
        # Check for CUET-related queries
        if 'cuet' in query_lower:
            cuet_context = self._get_cuet_context()
            if cuet_context:
                pdf_context_parts.append(cuet_context)
        
        # Check for counselling-related queries
        if any(word in query_lower for word in ['counselling', 'counseling', 'admission process', 'seat allocation']):
            counselling_context = self._get_counselling_context()
            if counselling_context:
                pdf_context_parts.append(counselling_context)
        
        return "\n".join(pdf_context_parts)
    
    def _format_bba_context(self, bba_info: Dict[str, Any], query: str) -> str:
        """Format BBA-specific context from PDF"""
        context = "\n📚 **BBA PROGRAM (From Official Documents):**\n"
        
        # Basic course info
        context += f"• **Course:** {bba_info.get('course_name', 'BBA')}\n"
        context += f"• **Duration:** {bba_info.get('duration', '4 years')}\n"
        context += f"• **Department:** {bba_info.get('department', 'Management Studies')}\n"
        context += f"• **Total Seats:** {bba_info.get('total_seats', '120')}\n"
        context += f"• **Entrance Exam:** {bba_info.get('entrance_exam', 'CUET (UG) 2025')}\n"
        
        # Eligibility details
        eligibility = bba_info.get('eligibility', {})
        if eligibility:
            context += f"\n**📋 Eligibility:**\n"
            context += f"• **Qualification:** {eligibility.get('qualification', 'N/A')}\n"
            context += f"• **Minimum Marks:** {eligibility.get('minimum_marks', 'N/A')}\n"
        
        # CUET requirements
        cuet_req = bba_info.get('cuet_requirements', {})
        if cuet_req:
            context += f"\n**📝 CUET Requirements:**\n"
            context += f"• **Domain Subject:** {cuet_req.get('domain_subject', 'N/A')}\n"
            context += f"• **General Test:** {cuet_req.get('general_test', 'N/A')}\n"
        
        # Admission process
        admission = bba_info.get('admission_process', {})
        if admission:
            context += f"\n**🎯 Admission Process:**\n"
            context += f"• **Application:** {admission.get('application', 'N/A')}\n"
            context += f"• **Counselling Portal:** {admission.get('counselling_portal', 'N/A')}\n"
        
        return context
    
    def _format_bpharma_context(self, bpharma_info: Dict[str, Any], query: str) -> str:
        """Format B.Pharma-specific context from PDF"""
        context = "\n💊 **B.PHARMA PROGRAM (From Official Documents):**\n"
        
        # Basic course info
        context += f"• **Course:** {bpharma_info.get('course_name', 'B.Pharma')}\n"
        context += f"• **Entrance Exam:** {bpharma_info.get('entrance_exam', 'CUET (UG) 2025')}\n"
        context += f"• **Program Type:** {bpharma_info.get('program_type', 'Single-branch program')}\n"
        context += f"• **Lateral Entry:** {bpharma_info.get('lateral_entry', 'Available for 2nd year')}\n"
        
        # Admission process
        admission = bpharma_info.get('admission_process', {})
        if admission:
            context += f"\n**🎯 Admission Process:**\n"
            context += f"• **Counselling Portal:** {admission.get('counselling_portal', 'N/A')}\n"
        
        # Counselling options
        counselling_opts = bpharma_info.get('counselling_options', {})
        if counselling_opts:
            context += f"\n**⚖️ Counselling Options:**\n"
            context += f"• **FREEZE:** {counselling_opts.get('freeze', 'N/A')}\n"
            context += f"• **FLOAT:** {counselling_opts.get('float', 'N/A')}\n"
        
        # Document requirements
        docs = bpharma_info.get('document_requirements', {})
        if docs and any(word in query for word in ['document', 'certificate', 'date']):
            context += f"\n**📄 Document Requirements:**\n"
            for doc_type, requirement in docs.items():
                context += f"• **{doc_type.replace('_', ' ').title()}:** {requirement}\n"
        
        return context
    
    def _get_cuet_context(self) -> str:
        """Get CUET-specific context from PDF knowledge"""
        context = "\n🎓 **CUET INFORMATION (From Official Documents):**\n"
        
        # BBA CUET requirements
        bba_info = self.pdf_knowledge.get('bba', {})
        if bba_info:
            context += f"\n**For BBA:**\n"
            context += f"• CUET (UG) 2025 is mandatory\n"
            cuet_req = bba_info.get('cuet_requirements', {})
            if cuet_req:
                context += f"• Domain Subject: {cuet_req.get('domain_subject', 'N/A')}\n"
                context += f"• General Test: {cuet_req.get('general_test', 'N/A')}\n"
        
        # B.Pharma CUET requirements
        bpharma_info = self.pdf_knowledge.get('bpharma', {})
        if bpharma_info:
            context += f"\n**For B.Pharma:**\n"
            context += f"• CUET (UG) 2025 qualification required\n"
            context += f"• Registration on counselling portal mandatory\n"
        
        return context
    
    def _get_counselling_context(self) -> str:
        """Get counselling-specific context from PDF knowledge"""
        context = "\n🏛️ **COUNSELLING PROCESS (From Official Documents):**\n"
        
        # Common counselling portal
        context += f"• **Official Portal:** https://mmmut.admissions.nic.in/\n"
        context += f"• **Registration:** Mandatory for all CUET qualified candidates\n"
        
        # B.Pharma specific counselling info
        bpharma_info = self.pdf_knowledge.get('bpharma', {})
        if bpharma_info:
            counselling_opts = bpharma_info.get('counselling_options', {})
            if counselling_opts:
                context += f"\n**Seat Allocation Options:**\n"
                context += f"• **FREEZE:** {counselling_opts.get('freeze', 'Keep allocated seat')}\n"
                context += f"• **FLOAT:** {counselling_opts.get('float', 'Participate in further rounds')}\n"
        
        context += f"\n**Important Notes:**\n"
        context += f"• Physical spot round available if seats remain vacant\n"
        context += f"• Seat acceptance fee required to confirm allocation\n"
        context += f"• Multiple payments will be refunded (keep only one successful transaction)\n"
        
        return context
    
    def _identify_relevant_categories_with_priority(self, query: str) -> List[tuple]:
        """Identify relevant data categories with priority scoring"""
        category_keywords = {
            'courses': {
                'primary': ['course', 'program', 'branch', 'btech', 'engineering', 'bba', 'b.pharma', 'pharma'],
                'secondary': ['cse', 'computer science', 'mechanical', 'civil', 'electrical', 'ece', 'it', 'business administration', 'management', 'pharmacy'],
                'tertiary': ['degree', 'study', 'stream']
            },
            'eligibility': {
                'primary': ['eligibility', 'criteria', 'qualification', 'requirement'],
                'secondary': ['marks', 'percentage', '10+2', 'jee', 'cuet', 'cutoff'],
                'tertiary': ['eligible', 'qualify', 'minimum']
            },
            'fees': {
                'primary': ['fee', 'cost', 'payment', 'money'],
                'secondary': ['scholarship', 'financial', 'tuition', 'hostel fee'],
                'tertiary': ['expensive', 'cheap', 'afford']
            },
            'important_dates': {
                'primary': ['date', 'deadline', 'when', 'schedule'],
                'secondary': ['timeline', 'last date', 'application date', 'exam date', 'cuet', 'jee main'],
                'tertiary': ['time', 'period', 'duration']
            },
            'admission_process': {
                'primary': ['admission', 'apply', 'application', 'procedure'],
                'secondary': ['form', 'process', 'steps', 'how to apply'],
                'tertiary': ['join', 'enroll', 'register']
            },
            'facilities': {
                'primary': ['facility', 'hostel', 'library', 'lab'],
                'secondary': ['infrastructure', 'campus', 'accommodation', 'mess'],
                'tertiary': ['wifi', 'sports', 'gym', 'canteen']
            },
            'placement': {
                'primary': ['placement', 'job', 'career', 'salary'],
                'secondary': ['package', 'company', 'recruitment', 'internship'],
                'tertiary': ['employment', 'work', 'future']
            },
            'contact': {
                'primary': ['contact', 'phone', 'email', 'address'],
                'secondary': ['office', 'reach', 'call', 'helpline'],
                'tertiary': ['help', 'support', 'assistance']
            }
        }
        
        category_scores = {}
        query_lower = query.lower()
        
        for category, keyword_groups in category_keywords.items():
            score = 0
            # Primary keywords get 3 points
            for keyword in keyword_groups.get('primary', []):
                if keyword in query_lower:
                    score += 3
            
            # Secondary keywords get 2 points
            for keyword in keyword_groups.get('secondary', []):
                if keyword in query_lower:
                    score += 2
            
            # Tertiary keywords get 1 point
            for keyword in keyword_groups.get('tertiary', []):
                if keyword in query_lower:
                    score += 1
            
            if score > 0:
                category_scores[category] = score
        
        # Sort by score (highest first) and return top categories
        sorted_categories = sorted(category_scores.items(), key=lambda x: x[1], reverse=True)
        
        # If no specific category found, include general categories
        if not sorted_categories:
            return [('courses', 1), ('eligibility', 1), ('fees', 1)]
        
        return sorted_categories[:4]  # Return top 4 relevant categories
    
    def _identify_relevant_categories(self, query: str) -> List[str]:
        """Legacy method for backward compatibility"""
        categories_with_priority = self._identify_relevant_categories_with_priority(query)
        return [category for category, _ in categories_with_priority]
    
    def _find_relevant_faqs(self, query: str) -> List[Dict]:
        """Find relevant FAQs based on query with improved scoring"""
        relevant_faqs = []
        query_lower = query.lower()
        query_words = set(query_lower.split())
        
        for faq in self.faqs:
            question_lower = faq['question'].lower()
            question_words = set(question_lower.split())
            
            # Calculate different types of relevance
            word_overlap = len(query_words.intersection(question_words))
            substring_match = 1 if any(word in question_lower for word in query_words if len(word) > 3) else 0
            category_match = 1 if any(cat in query_lower for cat in ['course', 'fee', 'eligibility', 'date', 'placement', 'hostel', 'contact']) else 0
            
            # Combined relevance score
            relevance_score = word_overlap * 2 + substring_match + category_match
            
            if relevance_score > 0:
                relevant_faqs.append((faq, relevance_score))
        
        # Sort by relevance and return top matches
        relevant_faqs.sort(key=lambda x: x[1], reverse=True)
        return [faq[0] for faq in relevant_faqs[:3]]  # Return top 3 most relevant FAQs
    
    def _format_category_data(self, category: str, data: Dict) -> str:
        """Format category data for better presentation in context"""
        if category == 'courses':
            courses_text = "📚 **COURSES OFFERED AT MMMUT:**\n\n"
            
            if 'undergraduate' in data:
                undergraduate = data['undergraduate']
                
                # B.Tech Engineering
                if 'engineering' in undergraduate:
                    eng_data = undergraduate['engineering']
                    courses_text += "**🎓 B.Tech Engineering Programs:**\n"
                    if 'courses' in eng_data:
                        for course in eng_data['courses']:
                            courses_text += f"• {course}\n"
                    courses_text += f"• Duration: {eng_data.get('duration', '4 years')}\n"
                    courses_text += f"• Entrance: {eng_data.get('entrance_exam', 'JEE Main')}\n"
                    courses_text += f"• Eligibility: {eng_data.get('eligibility', '10+2 with PCM')}\n\n"
                
                # BBA
                if 'management' in undergraduate:
                    mgmt_data = undergraduate['management']
                    courses_text += "**💼 Management Program:**\n"
                    if 'courses' in mgmt_data:
                        for course in mgmt_data['courses']:
                            courses_text += f"• {course}\n"
                    courses_text += f"• Duration: {mgmt_data.get('duration', '3 years')}\n"
                    courses_text += f"• Entrance: {mgmt_data.get('entrance_exam', 'CUET')}\n"
                    courses_text += f"• Eligibility: {mgmt_data.get('eligibility', '10+2 any stream')}\n\n"
                
                # B.Pharma
                if 'pharmacy' in undergraduate:
                    pharma_data = undergraduate['pharmacy']
                    courses_text += "**💊 Pharmacy Program:**\n"
                    if 'courses' in pharma_data:
                        for course in pharma_data['courses']:
                            courses_text += f"• {course}\n"
                    courses_text += f"• Duration: {pharma_data.get('duration', '4 years')}\n"
                    courses_text += f"• Entrance: {pharma_data.get('entrance_exam', 'CUET')}\n"
                    courses_text += f"• Eligibility: {pharma_data.get('eligibility', '10+2 with PCB/PCM')}\n\n"
            
            # Add entrance exam info if available
            if 'entrance_exams' in data:
                courses_text += "**📝 Entrance Exams:**\n"
                for exam, exam_data in data['entrance_exams'].items():
                    courses_text += f"• **{exam.replace('_', ' ')}:** For {', '.join(exam_data.get('for_courses', []))}\n"
            
            return courses_text
            
        elif category == 'eligibility':
            eligibility_text = "📋 **ELIGIBILITY CRITERIA:**\n\n"
            
            # B.Tech Engineering
            if 'btech_engineering' in data:
                btech_data = data['btech_engineering']
                eligibility_text += "**🎓 B.Tech Engineering:**\n"
                eligibility_text += f"• Academic Qualification: {btech_data.get('academic_qualification', '10+2')}\n"
                eligibility_text += f"• Minimum Marks: {btech_data.get('minimum_marks', '75%')}\n"
                eligibility_text += f"• Required Subjects: {btech_data.get('required_subjects', 'PCM')}\n"
                eligibility_text += f"• Entrance Exam: {btech_data.get('entrance_exam', 'JEE Main')}\n"
                eligibility_text += f"• Age Limit: {btech_data.get('age_limit', 'No age limit')}\n\n"
            
            # BBA
            if 'bba' in data:
                bba_data = data['bba']
                eligibility_text += "**💼 BBA:**\n"
                eligibility_text += f"• Academic Qualification: {bba_data.get('academic_qualification', '10+2')}\n"
                eligibility_text += f"• Minimum Marks: {bba_data.get('minimum_marks', '50%')}\n"
                eligibility_text += f"• Required Subjects: {bba_data.get('required_subjects', 'Any stream')}\n"
                eligibility_text += f"• Entrance Exam: {bba_data.get('entrance_exam', 'CUET')}\n"
                eligibility_text += f"• Age Limit: {bba_data.get('age_limit', 'No age limit')}\n\n"
            
            # B.Pharma
            if 'b_pharma' in data:
                pharma_data = data['b_pharma']
                eligibility_text += "**💊 B.Pharma:**\n"
                eligibility_text += f"• Academic Qualification: {pharma_data.get('academic_qualification', '10+2')}\n"
                eligibility_text += f"• Minimum Marks: {pharma_data.get('minimum_marks', '50%')}\n"
                eligibility_text += f"• Required Subjects: {pharma_data.get('required_subjects', 'PCB/PCM')}\n"
                eligibility_text += f"• Entrance Exam: {pharma_data.get('entrance_exam', 'CUET')}\n"
                eligibility_text += f"• Age Limit: {pharma_data.get('age_limit', 'No age limit')}\n\n"
            
            eligibility_text += "**🌍 Nationality:** Indian/NRI/Foreign nationals eligible for all courses"
            return eligibility_text
            
        elif category == 'fees':
            fees_text = "💰 **FEE STRUCTURE:**\n\n"
            
            # B.Tech Engineering
            if 'btech_engineering' in data:
                btech_data = data['btech_engineering']
                fees_text += "**🎓 B.Tech Engineering (Annual):**\n"
                fees_text += f"• Tuition Fee: ₹{btech_data.get('tuition_fee', '50,000')}\n"
                fees_text += f"• Hostel Fee: ₹{btech_data.get('hostel_fee', '25,000')}\n"
                fees_text += f"• Mess Fee: ₹{btech_data.get('mess_fee', '20,000')}\n"
                fees_text += f"• Other Charges: ₹{btech_data.get('other_charges', '5,000')}\n"
                fees_text += f"• **Total Annual: ₹{btech_data.get('total_annual_fee', '1,00,000')}**\n"
                fees_text += f"• **Total Course Fee ({btech_data.get('duration', '4 years')}): ₹{btech_data.get('total_course_fee', '4,00,000')}**\n\n"
            
            # BBA
            if 'bba' in data:
                bba_data = data['bba']
                fees_text += "**💼 BBA (Annual):**\n"
                fees_text += f"• Tuition Fee: ₹{bba_data.get('tuition_fee', '40,000')}\n"
                fees_text += f"• Hostel Fee: ₹{bba_data.get('hostel_fee', '25,000')}\n"
                fees_text += f"• Mess Fee: ₹{bba_data.get('mess_fee', '20,000')}\n"
                fees_text += f"• Other Charges: ₹{bba_data.get('other_charges', '5,000')}\n"
                fees_text += f"• **Total Annual: ₹{bba_data.get('total_annual_fee', '90,000')}**\n"
                fees_text += f"• **Total Course Fee ({bba_data.get('duration', '3 years')}): ₹{bba_data.get('total_course_fee', '2,70,000')}**\n\n"
            
            # B.Pharma
            if 'b_pharma' in data:
                pharma_data = data['b_pharma']
                fees_text += "**💊 B.Pharma (Annual):**\n"
                fees_text += f"• Tuition Fee: ₹{pharma_data.get('tuition_fee', '60,000')}\n"
                fees_text += f"• Hostel Fee: ₹{pharma_data.get('hostel_fee', '25,000')}\n"
                fees_text += f"• Mess Fee: ₹{pharma_data.get('mess_fee', '20,000')}\n"
                fees_text += f"• Other Charges: ₹{pharma_data.get('other_charges', '5,000')}\n"
                fees_text += f"• **Total Annual: ₹{pharma_data.get('total_annual_fee', '1,10,000')}**\n"
                fees_text += f"• **Total Course Fee ({pharma_data.get('duration', '4 years')}): ₹{pharma_data.get('total_course_fee', '4,40,000')}**\n\n"
            
            # Common information
            if 'common_info' in data:
                common_data = data['common_info']
                fees_text += "**💳 Payment Information:**\n"
                fees_text += f"• Payment Modes: {', '.join(common_data.get('payment_modes', ['Online', 'DD']))}\n"
                fees_text += f"• Scholarship: {common_data.get('scholarship', 'Available for meritorious students')}\n"
                fees_text += f"• Installments: {common_data.get('installment_facility', 'Available')}\n"
                fees_text += f"• Refund Policy: {common_data.get('fee_refund_policy', 'As per university rules')}\n"
            
            return fees_text
            
        elif category == 'important_dates':
            dates_text = "📅 **IMPORTANT DATES 2024:**\n\n"
            
            # JEE Main for B.Tech
            if 'jee_main_btech' in data:
                jee_data = data['jee_main_btech']
                dates_text += "**🎓 JEE Main (B.Tech Engineering):**\n"
                dates_text += f"• Application Start: {jee_data.get('application_start', 'December 1, 2023')}\n"
                dates_text += f"• Application End: {jee_data.get('application_end', 'January 31, 2024')}\n"
                dates_text += f"• Exam Dates: {jee_data.get('exam_dates', 'January-April 2024')}\n"
                dates_text += f"• Result Declaration: {jee_data.get('result_declaration', 'February-April 2024')}\n"
                dates_text += f"• Counseling Start: {jee_data.get('counseling_start', 'June 1, 2024')}\n"
                dates_text += f"• Admission Confirmation: {jee_data.get('admission_confirmation', 'July 31, 2024')}\n"
                dates_text += f"• Classes Begin: {jee_data.get('classes_begin', 'August 15, 2024')}\n\n"
            
            # CUET for BBA & B.Pharma
            if 'cuet_bba_bpharma' in data:
                cuet_data = data['cuet_bba_bpharma']
                dates_text += "**💼💊 CUET (BBA & B.Pharma):**\n"
                dates_text += f"• Application Start: {cuet_data.get('application_start', 'February 15, 2024')}\n"
                dates_text += f"• Application End: {cuet_data.get('application_end', 'March 31, 2024')}\n"
                dates_text += f"• Exam Dates: {cuet_data.get('exam_dates', 'May 15-31, 2024')}\n"
                dates_text += f"• Result Declaration: {cuet_data.get('result_declaration', 'June 30, 2024')}\n"
                dates_text += f"• Counseling Start: {cuet_data.get('counseling_start', 'July 15, 2024')}\n"
                dates_text += f"• Admission Confirmation: {cuet_data.get('admission_confirmation', 'August 15, 2024')}\n"
                dates_text += f"• Classes Begin: {cuet_data.get('classes_begin', 'September 1, 2024')}\n\n"
            
            # General timeline
            if 'general_timeline' in data:
                general_data = data['general_timeline']
                dates_text += "**📋 General Information:**\n"
                dates_text += f"• Document Verification: {general_data.get('document_verification', 'After counseling')}\n"
                dates_text += f"• Fee Payment Deadline: {general_data.get('fee_payment_deadline', 'Within 7 days')}\n"
                dates_text += f"• Late Admission: {general_data.get('late_admission', 'Subject to availability')}\n"
            
            return dates_text
            
        elif category == 'placement':
            recruiters = data.get('top_recruiters', [])
            recruiters_text = ', '.join(recruiters[:5]) + (f" and {len(recruiters)-5} more" if len(recruiters) > 5 else "")
            return f"""
🎯 **PLACEMENT STATISTICS:**
• Placement Rate: {data.get('placement_percentage', '85%')}
• Average Package: {data.get('average_package', '6.5 LPA')}
• Highest Package: {data.get('highest_package', '25 LPA')}
• Top Recruiters: {recruiters_text}
• Placement Cell: {data.get('placement_cell', 'Active placement cell')}
• Training: {data.get('training_programs', 'Available')}
• Internships: {data.get('internship_opportunities', 'Available')}
"""
            
        elif category == 'facilities':
            academic = data.get('academic', [])
            residential = data.get('residential', [])
            recreational = data.get('recreational', [])
            return f"""
🏫 **CAMPUS FACILITIES:**
**Academic:**
{chr(10).join(f'• {facility}' for facility in academic)}

**Residential:**
{chr(10).join(f'• {facility}' for facility in residential)}

**Recreational:**
{chr(10).join(f'• {facility}' for facility in recreational)}
"""
            
        elif category == 'contact':
            admission_office = data.get('admission_office', {})
            return f"""
📞 **CONTACT INFORMATION:**
• Phone: {admission_office.get('phone', '+91-551-2273958')}
• Email: {admission_office.get('email', '<EMAIL>')}
• Address: {admission_office.get('address', 'MMMUT, Gorakhpur - 273010, UP')}
• Website: {data.get('university_website', 'https://www.mmmut.ac.in')}
• Office Hours: {data.get('office_hours', '9:00 AM to 5:00 PM (Mon-Fri)')}
• Helpline: {data.get('helpline', '1800-XXX-XXXX')}
"""
            
        elif category == 'admission_process':
            steps = data.get('steps', [])
            steps_text = "\n".join(f"{i+1}. {step}" for i, step in enumerate(steps))
            return f"""
📝 **ADMISSION PROCESS:**
{steps_text}

• Entrance Exam: {data.get('entrance_exam', 'JEE Main')}
• Selection Criteria: {data.get('selection_criteria', 'Merit-based')}
• Reservation: {data.get('reservation', 'As per government norms')}
"""
        
        # Fallback for other categories
        return f"\n**{category.upper().replace('_', ' ')} INFORMATION:**\n{json.dumps(data, indent=2)}"
    
    def _get_recent_conversation_context(self) -> str:
        """Get context from recent conversation history"""
        if not self.conversation_history:
            return ""
        
        # Get last 2 conversations for context
        recent_conversations = self.conversation_history[-2:]
        context_text = ""
        
        for i, conv in enumerate(recent_conversations, 1):
            context_text += f"Previous Q{i}: {conv['query'][:100]}...\n"
            context_text += f"Previous A{i}: {conv['response'][:150]}...\n\n"
        
        return context_text
    
    def _create_prompt(self, query: str, context: str) -> str:
        """Create highly specific and actionable prompt for Gemini AI"""
        # Analyze query intent and extract key information
        query_intent = self._analyze_query_intent(query)
        query_keywords = self._extract_query_keywords(query)
        specific_requirements = self._identify_specific_requirements(query)

        prompt = f"""
{self.system_prompt}

🎯 **CURRENT QUERY ANALYSIS:**
• Intent: {query_intent}
• Key Topics: {', '.join(query_keywords)}
• Specific Requirements: {specific_requirements}

📚 **AVAILABLE KNOWLEDGE BASE:**
{context}

❓ **STUDENT'S EXACT QUESTION:** "{query}"

🚀 **RESPONSE REQUIREMENTS (MANDATORY):**

1. **IMMEDIATE ANSWER**: Start with the exact answer to their question in the first sentence
2. **SPECIFIC DETAILS**: Include ALL relevant numbers, dates, fees, requirements from the knowledge base
3. **STRUCTURED FORMAT**: Use the following structure:
   - Direct answer with key information
   - Detailed breakdown with specific data
   - Step-by-step next actions
   - Contact information when relevant
   - Encouraging closing with offer for more help

4. **MUST INCLUDE WHEN AVAILABLE:**
   - Exact fees with breakdown (₹50,000 tuition + ₹25,000 hostel + ₹20,000 mess + ₹5,000 other = ₹1,00,000 total)
   - Specific dates (Application: March 1-April 30, 2024)
   - Precise eligibility (75% in 10+2 with PCM, JEE Main required)
   - Contact details (+91-551-2273958, <EMAIL>)
   - Course names (Computer Science, IT, ECE, EE, ME, CE, Chemical, Biotechnology)
   - Placement statistics (85% placement, 6.5 LPA average, 25 LPA highest)

5. **FORMATTING RULES:**
   - Use emojis for section headers (🎓📋💰📅📞🏫🎯)
   - Bold important numbers and dates
   - Bullet points for lists
   - Numbered steps for processes
   - Clear section breaks

6. **TONE & STYLE:**
   - Confident and knowledgeable
   - Specific and actionable
   - Encouraging and supportive
   - Professional yet approachable

7. **AVOID:**
   - Generic statements like "contact for more details"
   - Vague responses without specific information
   - Long paragraphs without structure
   - Information not in the knowledge base

8. **END WITH:**
   - Specific next steps they should take
   - Relevant contact information
   - Offer to help with related questions

Generate a response that is so comprehensive and specific that the student has all the information they need to take immediate action.
"""
        return prompt

    def _analyze_query_intent(self, query: str) -> str:
        """Analyze the primary intent of the user's query with enhanced detection"""
        query_lower = query.lower()

        intent_patterns = {
            'Course Information Request': ['course', 'program', 'branch', 'engineering', 'btech', 'what courses', 'which courses'],
            'Eligibility Verification': ['eligibility', 'qualify', 'marks', 'percentage', 'criteria', 'can i apply', 'am i eligible'],
            'Fee Structure Inquiry': ['fee', 'cost', 'payment', 'scholarship', 'financial', 'how much', 'money'],
            'Admission Process Query': ['admission', 'apply', 'application', 'procedure', 'form', 'how to apply', 'process'],
            'Important Dates Request': ['date', 'deadline', 'when', 'last date', 'timeline', 'schedule'],
            'Facility Information': ['hostel', 'library', 'lab', 'facility', 'campus', 'infrastructure'],
            'Placement Statistics': ['placement', 'job', 'career', 'company', 'salary', 'package'],
            'Contact Information': ['contact', 'phone', 'email', 'address', 'reach', 'call'],
            'University Overview': ['about', 'university', 'college', 'mmmut', 'tell me about']
        }

        # Score each intent based on keyword matches
        intent_scores = {}
        for intent, keywords in intent_patterns.items():
            score = sum(2 if keyword in query_lower else 0 for keyword in keywords)
            if score > 0:
                intent_scores[intent] = score

        if intent_scores:
            return max(intent_scores.items(), key=lambda x: x[1])[0]
        
        return "General Information Request"
    
    def _extract_query_keywords(self, query: str) -> List[str]:
        """Extract key topics and keywords from the query"""
        query_lower = query.lower()
        
        # Define important keywords to extract
        important_keywords = {
            'courses': ['cse', 'computer science', 'mechanical', 'civil', 'electrical', 'ece', 'it', 'chemical', 'biotechnology', 'bba', 'business administration', 'b.pharma', 'pharmacy', 'management'],
            'academic': ['btech', 'b.tech', 'engineering', 'degree', '10+2', 'jee', 'cuet', 'marks', 'percentage'],
            'financial': ['fee', 'cost', 'scholarship', 'payment', 'money', 'tuition', 'hostel fee'],
            'timeline': ['date', 'deadline', 'when', 'schedule', 'application', 'exam', 'jee main', 'cuet'],
            'facilities': ['hostel', 'library', 'lab', 'campus', 'mess', 'wifi'],
            'career': ['placement', 'job', 'salary', 'package', 'company', 'career'],
            'contact': ['phone', 'email', 'address', 'contact', 'office']
        }
        
        found_keywords = []
        for category, keywords in important_keywords.items():
            for keyword in keywords:
                if keyword in query_lower:
                    found_keywords.append(keyword)
        
        # Also extract specific course mentions
        courses = ['computer science', 'mechanical', 'civil', 'electrical', 'electronics', 'information technology']
        for course in courses:
            if course in query_lower:
                found_keywords.append(course)
        
        return list(set(found_keywords))  # Remove duplicates
    
    def _identify_specific_requirements(self, query: str) -> str:
        """Identify specific requirements or constraints mentioned in the query"""
        query_lower = query.lower()
        requirements = []
        
        # Check for specific requirements
        if any(word in query_lower for word in ['urgent', 'quickly', 'asap', 'immediately']):
            requirements.append("Urgent response needed")
        
        if any(word in query_lower for word in ['detailed', 'complete', 'full', 'comprehensive']):
            requirements.append("Detailed information requested")
        
        if any(word in query_lower for word in ['step by step', 'process', 'procedure', 'how to']):
            requirements.append("Step-by-step guidance needed")
        
        if any(word in query_lower for word in ['compare', 'difference', 'vs', 'versus']):
            requirements.append("Comparison requested")
        
        if any(word in query_lower for word in ['latest', 'current', 'updated', '2024', '2025']):
            requirements.append("Current/latest information needed")
        
        # Check for specific course mentions
        courses = ['cse', 'computer science', 'mechanical', 'civil', 'electrical', 'ece', 'it', 'bba', 'business administration', 'b.pharma', 'pharmacy', 'management']
        mentioned_courses = [course for course in courses if course in query_lower]
        if mentioned_courses:
            requirements.append(f"Specific to: {', '.join(mentioned_courses)}")
        
        return "; ".join(requirements) if requirements else "General information request"
    
    def _add_to_history(self, query: str, response: str):
        """Add query and response to conversation history"""
        self.conversation_history.append({
            "timestamp": datetime.now().isoformat(),
            "query": query,
            "response": response,
            "query_number": self.query_count
        })
        
        # Keep only last 10 conversations to manage memory
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create error response"""
        return {
            "response": "I apologize, but I'm experiencing some technical difficulties. Please try again in a moment or contact the admission office directly.",
            "response_type": "error",
            "confidence": 0.0,
            "sources": ["error_handler"],
            "timestamp": datetime.now().isoformat(),
            "error": error_message
        }
    
    def get_conversation_history(self) -> List[Dict]:
        """Get conversation history"""
        return self.conversation_history
    
    def reset_conversation(self):
        """Reset conversation history"""
        self.conversation_history = []
        self.query_count = 0
        self.session_start_time = datetime.now()
        
        # Reset chat session
        self.chat = self.model.start_chat(history=[])
        
        logger.info("Conversation reset")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get chatbot usage statistics"""
        return {
            "total_queries": self.query_count,
            "session_duration": str(datetime.now() - self.session_start_time),
            "session_start": self.session_start_time.isoformat(),
            "conversation_length": len(self.conversation_history),
            "data_categories": len(self.organized_data.get("categories", {})),
            "available_faqs": len(self.faqs)
        }


def main():
    """Main function for testing the chatbot"""
    try:
        # Initialize chatbot
        chatbot = AdmissionChatbot()
        
        print("MMMUT Admission Chatbot")
        print("=" * 50)
        print("Type 'quit' to exit, 'reset' to reset conversation, 'stats' for statistics")
        print()
        
        while True:
            user_input = input("You: ").strip()
            
            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'reset':
                chatbot.reset_conversation()
                print("Conversation reset!")
                continue
            elif user_input.lower() == 'stats':
                stats = chatbot.get_statistics()
                print(f"Statistics: {json.dumps(stats, indent=2)}")
                continue
            elif not user_input:
                continue
            
            # Process query
            response_data = chatbot.process_query(user_input)
            
            # Display response
            print(f"Bot: {response_data['response']}")
            print(f"[Type: {response_data['response_type']}, Confidence: {response_data['confidence']:.2f}]")
            print()
    
    except KeyboardInterrupt:
        print("\nGoodbye!")
    except Exception as e:
        print(f"Error: {str(e)}")
        logger.error(f"Main function error: {str(e)}")


if __name__ == "__main__":
    main()