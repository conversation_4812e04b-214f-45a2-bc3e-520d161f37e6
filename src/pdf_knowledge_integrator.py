"""
PDF Knowledge Integrator for MMMUT Chatbot
Processes extracted PDF content and integrates it with the chatbot's knowledge base
"""

import json
import re
from typing import Dict, List, Any
from pathlib import Path

class PDFKnowledgeIntegrator:
    def __init__(self, pdf_data_file: str = "data/pdf_extracted_data.json"):
        self.pdf_data_file = Path(pdf_data_file)
        self.pdf_data = self.load_pdf_data()
        self.structured_knowledge = {}
        
    def load_pdf_data(self) -> Dict[str, Any]:
        """Load extracted PDF data"""
        if self.pdf_data_file.exists():
            with open(self.pdf_data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def process_all_documents(self) -> Dict[str, Any]:
        """Process all PDF documents and create structured knowledge"""
        
        # Process BBA document
        if "1st" in self.pdf_data:
            self.structured_knowledge["bba"] = self.process_bba_document(self.pdf_data["1st"])
        
        # Process B.Pharma document
        if "BPharma" in self.pdf_data:
            self.structured_knowledge["bpharma"] = self.process_bpharma_document(self.pdf_data["BPharma"])
        
        # Process B.Tech document
        if "B.Tech.firstYearLateralEntry" in self.pdf_data:
            self.structured_knowledge["btech"] = self.process_btech_document(self.pdf_data["B.Tech.firstYearLateralEntry"])
        
        # Process Admission Brochure
        if "Admission Brochure 2025" in self.pdf_data:
            self.structured_knowledge["admission_brochure"] = self.process_admission_brochure(self.pdf_data["Admission Brochure 2025"])
        
        return self.structured_knowledge
    
    def process_bba_document(self, doc_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process BBA FAQ document"""
        text = doc_data.get("raw_text", "")
        
        bba_info = {
            "course_name": "Bachelor of Business Administration (BBA)",
            "duration": "4 years (8 semesters)",
            "department": "Department of Management Studies",
            "total_seats": "120",
            "entrance_exam": "CUET (UG) 2025",
            "eligibility": {
                "qualification": "10+2 (Intermediate or equivalent) in any stream",
                "minimum_marks": "55% marks (50% for SC/ST candidates)",
                "mandatory_exam": "CUET (UG) 2025"
            },
            "cuet_requirements": {
                "domain_subject": "Any one subject from Domain Category (Paper Code: 301-326)",
                "general_test": "General Aptitude Test (Paper Code: 501)"
            },
            "admission_process": {
                "application": "Online through www.mmmut.ac.in",
                "counselling_portal": "https://mmmut.admissions.nic.in/",
                "counselling_procedure": "Refer to Clause 6.6 of Undergraduate Admission Brochure 2025-26"
            },
            "important_points": [
                "CUET (UG) 2025 is mandatory for BBA admission",
                "Registration on counselling portal is mandatory",
                "Physical spot round available if seats remain vacant",
                "Fee structure available on university website"
            ],
            "faqs": self.extract_bba_faqs(text)
        }
        
        return bba_info
    
    def process_bpharma_document(self, doc_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process B.Pharma FAQ document"""
        text = doc_data.get("raw_text", "")
        
        bpharma_info = {
            "course_name": "Bachelor of Pharmacy (B.Pharma)",
            "entrance_exam": "CUET (UG) 2025",
            "program_type": "Single-branch program",
            "lateral_entry": "Available for 2nd year",
            "admission_process": {
                "counselling_portal": "https://mmmut.admissions.nic.in/",
                "counselling_procedure": "Refer to Sections 4.6 and 5.6 of Undergraduate Admission Brochure 2025-26"
            },
            "counselling_options": {
                "freeze": "Keep allocated seat, no participation in further rounds",
                "float": "Participate in further rounds for better preference"
            },
            "important_points": [
                "CUET (UG) 2025 qualification required",
                "Registration on counselling portal is mandatory",
                "Branch change not applicable (single-branch program)",
                "Physical spot round available if seats remain vacant",
                "Lateral entry available for 2nd year"
            ],
            "document_requirements": {
                "obc_ews_certificates": "Date of issue should be on or after April 1, 2025",
                "sc_st_certificates": "No date restriction",
                "domicile_certificate": "No date restriction"
            },
            "faqs": self.extract_bpharma_faqs(text)
        }
        
        return bpharma_info
    
    def process_btech_document(self, doc_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process B.Tech document"""
        text = doc_data.get("raw_text", "")
        
        btech_info = {
            "course_name": "Bachelor of Technology (B.Tech)",
            "lateral_entry": "Available for 1st year",
            "source_document": "B.Tech First Year Lateral Entry FAQ",
            "raw_content_available": True
        }
        
        return btech_info
    
    def process_admission_brochure(self, doc_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Admission Brochure"""
        text = doc_data.get("raw_text", "")
        
        brochure_info = {
            "document_name": "Undergraduate Admission Brochure 2025-26",
            "year": "2025-26",
            "content_extracted": len(text) > 0,
            "raw_text_length": len(text)
        }
        
        return brochure_info
    
    def extract_bba_faqs(self, text: str) -> List[Dict[str, str]]:
        """Extract BBA FAQs from text"""
        faqs = []
        
        # Split text by question numbers
        questions = re.split(r'\d+\.\s+', text)[1:]  # Skip first empty split
        
        for i, question_text in enumerate(questions, 1):
            if 'Answer:' in question_text:
                parts = question_text.split('Answer:', 1)
                if len(parts) == 2:
                    question = parts[0].strip()
                    answer = parts[1].strip()
                    
                    faqs.append({
                        "question_number": i,
                        "question": question,
                        "answer": answer,
                        "category": self.categorize_bba_question(question)
                    })
        
        return faqs
    
    def extract_bpharma_faqs(self, text: str) -> List[Dict[str, str]]:
        """Extract B.Pharma FAQs from text"""
        faqs = []
        
        # Split text by question numbers
        questions = re.split(r'\d+\.\s+', text)[1:]  # Skip first empty split
        
        for i, question_text in enumerate(questions, 1):
            if 'Answer:' in question_text:
                parts = question_text.split('Answer:', 1)
                if len(parts) == 2:
                    question = parts[0].strip()
                    answer = parts[1].strip()
                    
                    faqs.append({
                        "question_number": i,
                        "question": question,
                        "answer": answer,
                        "category": self.categorize_bpharma_question(question)
                    })
        
        return faqs
    
    def categorize_bba_question(self, question: str) -> str:
        """Categorize BBA questions"""
        question_lower = question.lower()
        
        if any(word in question_lower for word in ['cuet', 'exam', 'subject']):
            return "entrance_exam"
        elif any(word in question_lower for word in ['eligibility', 'marks', 'qualification']):
            return "eligibility"
        elif any(word in question_lower for word in ['seats', 'duration', 'program']):
            return "course_details"
        elif any(word in question_lower for word in ['fee', 'cost', 'payment']):
            return "fees"
        elif any(word in question_lower for word in ['counselling', 'admission', 'apply']):
            return "admission_process"
        else:
            return "general"
    
    def categorize_bpharma_question(self, question: str) -> str:
        """Categorize B.Pharma questions"""
        question_lower = question.lower()
        
        if any(word in question_lower for word in ['counselling', 'portal', 'register']):
            return "counselling"
        elif any(word in question_lower for word in ['choice', 'lock', 'preference']):
            return "choice_filling"
        elif any(word in question_lower for word in ['seat', 'allot', 'freeze', 'float']):
            return "seat_allocation"
        elif any(word in question_lower for word in ['branch', 'change', 'lateral']):
            return "course_structure"
        elif any(word in question_lower for word in ['certificate', 'document', 'date']):
            return "documents"
        elif any(word in question_lower for word in ['fee', 'payment', 'refund']):
            return "fees"
        else:
            return "general"
    
    def create_enhanced_faqs(self) -> List[Dict[str, str]]:
        """Create enhanced FAQ list from PDF content"""
        enhanced_faqs = []
        
        # Add BBA FAQs
        if "bba" in self.structured_knowledge:
            bba_faqs = self.structured_knowledge["bba"].get("faqs", [])
            for faq in bba_faqs:
                enhanced_faqs.append({
                    "question": faq["question"],
                    "answer": faq["answer"],
                    "category": faq["category"],
                    "course": "BBA",
                    "source": "Official BBA FAQ Document"
                })
        
        # Add B.Pharma FAQs
        if "bpharma" in self.structured_knowledge:
            bpharma_faqs = self.structured_knowledge["bpharma"].get("faqs", [])
            for faq in bpharma_faqs:
                enhanced_faqs.append({
                    "question": faq["question"],
                    "answer": faq["answer"],
                    "category": faq["category"],
                    "course": "B.Pharma",
                    "source": "Official B.Pharma FAQ Document"
                })
        
        return enhanced_faqs
    
    def save_structured_knowledge(self, output_file: str = "data/pdf_structured_knowledge.json"):
        """Save structured knowledge to file"""
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.structured_knowledge, f, indent=2, ensure_ascii=False)
        
        print(f"Structured knowledge saved to: {output_path}")
    
    def get_course_specific_info(self, course: str) -> Dict[str, Any]:
        """Get specific information for a course"""
        course_lower = course.lower()
        
        if "bba" in course_lower:
            return self.structured_knowledge.get("bba", {})
        elif "pharma" in course_lower or "b.pharma" in course_lower:
            return self.structured_knowledge.get("bpharma", {})
        elif "btech" in course_lower or "engineering" in course_lower:
            return self.structured_knowledge.get("btech", {})
        else:
            return {}

if __name__ == "__main__":
    # Test the integrator
    integrator = PDFKnowledgeIntegrator()
    structured_knowledge = integrator.process_all_documents()
    integrator.save_structured_knowledge()
    
    print("\nStructured Knowledge Summary:")
    for course, info in structured_knowledge.items():
        print(f"\n{course.upper()}:")
        if "faqs" in info:
            print(f"  - {len(info['faqs'])} FAQs extracted")
        if "course_name" in info:
            print(f"  - Course: {info['course_name']}")
        if "entrance_exam" in info:
            print(f"  - Entrance: {info['entrance_exam']}")