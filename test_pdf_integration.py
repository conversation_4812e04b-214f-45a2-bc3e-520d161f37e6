#!/usr/bin/env python3
"""
Test script to verify PDF integration with the chatbot
"""

import sys
import os
sys.path.append('src')

from chatbot import AdmissionChatbot

def test_pdf_integration():
    """Test the PDF integration functionality"""
    print("🚀 Testing MMMUT Chatbot with PDF Integration\n")
    
    # Initialize chatbot
    try:
        chatbot = AdmissionChatbot()
        print("✅ Chatbot initialized successfully\n")
    except Exception as e:
        print(f"❌ Error initializing chatbot: {e}")
        return
    
    # Test queries
    test_queries = [
        "What is CUET?",
        "Tell me about BBA course",
        "What are the eligibility criteria for B.Pharma?",
        "How do I apply for BBA admission?",
        "What is the counselling process for B.Pharma?",
        "What documents are required for admission?",
        "Tell me about seat allocation process"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"🔍 Test Query {i}: {query}")
        print("-" * 50)
        
        try:
            response = chatbot.process_query(query)
            print(f"📝 Response:\n{response['response']}\n")
            print(f"📊 Response Type: {response.get('response_type', 'N/A')}")
            print(f"🎯 Confidence: {response.get('confidence', 'N/A')}")
            
            if 'sources' in response:
                print(f"📚 Sources: {response['sources']}")
            
        except Exception as e:
            print(f"❌ Error processing query: {e}")
        
        print("=" * 80)
        print()

if __name__ == "__main__":
    test_pdf_integration()