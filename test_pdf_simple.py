#!/usr/bin/env python3
"""
Simple test to verify PDF knowledge extraction
"""

import sys
import os
sys.path.append('src')

from pdf_knowledge_integrator import PDFKnowledgeIntegrator

def test_pdf_knowledge():
    """Test PDF knowledge extraction"""
    print("🚀 Testing PDF Knowledge Extraction\n")
    
    try:
        integrator = PDFKnowledgeIntegrator()
        knowledge = integrator.process_all_documents()
        
        print("✅ PDF Knowledge loaded successfully\n")
        
        # Test BBA information
        if 'bba' in knowledge:
            bba_info = knowledge['bba']
            print("📚 BBA Information:")
            print(f"  - Course: {bba_info.get('course_name', 'N/A')}")
            print(f"  - Duration: {bba_info.get('duration', 'N/A')}")
            print(f"  - Seats: {bba_info.get('total_seats', 'N/A')}")
            print(f"  - Entrance: {bba_info.get('entrance_exam', 'N/A')}")
            print(f"  - FAQs: {len(bba_info.get('faqs', []))}")
            print()
        
        # Test B.Pharma information
        if 'bpharma' in knowledge:
            bpharma_info = knowledge['bpharma']
            print("💊 B.Pharma Information:")
            print(f"  - Course: {bpharma_info.get('course_name', 'N/A')}")
            print(f"  - Entrance: {bpharma_info.get('entrance_exam', 'N/A')}")
            print(f"  - Program Type: {bpharma_info.get('program_type', 'N/A')}")
            print(f"  - FAQs: {len(bpharma_info.get('faqs', []))}")
            print()
        
        # Test FAQ creation
        enhanced_faqs = integrator.create_enhanced_faqs()
        print(f"📋 Total Enhanced FAQs: {len(enhanced_faqs)}")
        
        # Show sample FAQs
        if enhanced_faqs:
            print("\n🔍 Sample FAQs:")
            for i, faq in enumerate(enhanced_faqs[:3], 1):
                print(f"\n{i}. **{faq['course']}** - {faq['category']}")
                print(f"   Q: {faq['question'][:100]}...")
                print(f"   A: {faq['answer'][:100]}...")
                print(f"   Source: {faq['source']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_pdf_knowledge()
    if success:
        print("\n✅ PDF knowledge extraction test completed successfully!")
    else:
        print("\n❌ PDF knowledge extraction test failed!")